#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
气象降雨预报Web API服务器
提供数据接口供前端地图调用
"""

from flask import Flask, jsonify, render_template, request
from flask_cors import CORS
import json
import os
from data_processor import RainfallDataProcessor

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 全局数据处理器
processor = None


def initialize_processor():
    """初始化数据处理器"""
    global processor
    csv_file = '2025062420.010.csv'
    
    if os.path.exists(csv_file):
        processor = RainfallDataProcessor(csv_file)
        processor.load_csv_data()
        print("数据处理器初始化成功")
    else:
        print(f"错误: 找不到CSV文件 {csv_file}")


@app.route('/')
def index():
    """主页"""
    return render_template('index.html')


@app.route('/api/rainfall/data')
def get_rainfall_data():
    """获取降雨数据API"""
    try:
        if processor is None:
            return jsonify({'error': '数据处理器未初始化'}), 500
        
        # 获取查询参数
        format_type = request.args.get('format', 'points')  # points, geojson, heatmap
        
        if format_type == 'geojson':
            data = processor.get_geojson_data()
        elif format_type == 'heatmap':
            data = processor.get_heatmap_data()
        else:  # points
            data = processor.process_data_for_visualization()
        
        return jsonify({
            'success': True,
            'data': data,
            'format': format_type
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@app.route('/api/rainfall/stats')
def get_rainfall_stats():
    """获取降雨数据统计信息API"""
    try:
        if processor is None:
            return jsonify({'error': '数据处理器未初始化'}), 500
        
        stats = processor.get_statistics()
        
        return jsonify({
            'success': True,
            'stats': stats
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@app.route('/api/rainfall/bounds')
def get_map_bounds():
    """获取地图边界信息API"""
    try:
        if processor is None:
            return jsonify({'error': '数据处理器未初始化'}), 500
        
        bounds = {
            'southwest': [processor.lat_min, processor.lon_min],
            'northeast': [processor.lat_max, processor.lon_max],
            'center': [
                (processor.lat_min + processor.lat_max) / 2,
                (processor.lon_min + processor.lon_max) / 2
            ]
        }
        
        return jsonify({
            'success': True,
            'bounds': bounds
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@app.route('/api/rainfall/grid/<int:row>/<int:col>')
def get_grid_point(row, col):
    """获取特定网格点的详细信息"""
    try:
        if processor is None:
            return jsonify({'error': '数据处理器未初始化'}), 500
        
        if (row < 0 or row >= processor.lat_points or 
            col < 0 or col >= processor.lon_points):
            return jsonify({'error': '网格坐标超出范围'}), 400
        
        # 计算坐标
        lat = processor.lat_max - row * processor.grid_spacing
        lon = processor.lon_min + col * processor.grid_spacing
        
        # 获取降雨值
        rainfall = float(processor.rainfall_data[row, col])
        
        point_info = {
            'row': row,
            'col': col,
            'lat': lat,
            'lon': lon,
            'rainfall': rainfall,
            'grid_spacing': processor.grid_spacing
        }
        
        return jsonify({
            'success': True,
            'point': point_info
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({'error': '接口不存在'}), 404


@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    return jsonify({'error': '服务器内部错误'}), 500


if __name__ == '__main__':
    # 初始化数据处理器
    initialize_processor()
    
    # 启动Flask应用
    print("启动气象降雨预报可视化服务器...")
    print("访问 http://localhost:5000 查看地图")
    print("API接口:")
    print("  - GET /api/rainfall/data?format=[points|geojson|heatmap] - 获取降雨数据")
    print("  - GET /api/rainfall/stats - 获取统计信息")
    print("  - GET /api/rainfall/bounds - 获取地图边界")
    print("  - GET /api/rainfall/grid/<row>/<col> - 获取特定网格点信息")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
