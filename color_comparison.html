<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中国气象标准颜色方案对比</title>
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        h1 { text-align: center; color: #333; margin-bottom: 30px; }
        .comparison-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 40px; }
        .color-scheme { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .scheme-title { font-size: 18px; font-weight: bold; margin-bottom: 15px; text-align: center; }
        .color-bar { height: 40px; border-radius: 5px; margin-bottom: 15px; border: 1px solid #ddd; }
        .color-details { font-size: 12px; line-height: 1.6; }
        .color-item { display: flex; align-items: center; margin-bottom: 8px; }
        .color-swatch { width: 30px; height: 20px; border-radius: 3px; margin-right: 10px; border: 1px solid #ccc; }
        .intensity-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .intensity-table th, .intensity-table td { padding: 8px; text-align: left; border: 1px solid #ddd; }
        .intensity-table th { background: #f8f9fa; font-weight: bold; }
        .standards-section { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .rgb-values { font-family: monospace; font-size: 11px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌧️ 中国气象标准降雨颜色方案对比</h1>
        
        <!-- 标准说明 -->
        <div class="standards-section">
            <h2>中国气象局降雨强度分级标准</h2>
            <table class="intensity-table">
                <thead>
                    <tr>
                        <th>降雨等级</th>
                        <th>降雨量范围 (mm/h)</th>
                        <th>标准颜色</th>
                        <th>RGB值</th>
                        <th>十六进制</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>无降雨</td>
                        <td>&lt; 0.1</td>
                        <td><div class="color-swatch" style="background: #FFFFFF;"></div></td>
                        <td class="rgb-values">(255, 255, 255)</td>
                        <td class="rgb-values">#FFFFFF</td>
                    </tr>
                    <tr>
                        <td>小雨</td>
                        <td>0.1 - 2.5</td>
                        <td><div class="color-swatch" style="background: #9FEF8C;"></div></td>
                        <td class="rgb-values">(159, 239, 140)</td>
                        <td class="rgb-values">#9FEF8C</td>
                    </tr>
                    <tr>
                        <td>中雨</td>
                        <td>2.6 - 8.0</td>
                        <td><div class="color-swatch" style="background: #3BBA3B;"></div></td>
                        <td class="rgb-values">(59, 186, 59)</td>
                        <td class="rgb-values">#3BBA3B</td>
                    </tr>
                    <tr>
                        <td>大雨</td>
                        <td>8.1 - 15.9</td>
                        <td><div class="color-swatch" style="background: #5FBCFD;"></div></td>
                        <td class="rgb-values">(95, 188, 253)</td>
                        <td class="rgb-values">#5FBCFD</td>
                    </tr>
                    <tr>
                        <td>暴雨</td>
                        <td>16.0 - 30.0</td>
                        <td><div class="color-swatch" style="background: #0100FB;"></div></td>
                        <td class="rgb-values">(1, 0, 251)</td>
                        <td class="rgb-values">#0100FB</td>
                    </tr>
                    <tr>
                        <td>大暴雨</td>
                        <td>30.1 - 70.0</td>
                        <td><div class="color-swatch" style="background: #F801F5;"></div></td>
                        <td class="rgb-values">(248, 1, 245)</td>
                        <td class="rgb-values">#F801F5</td>
                    </tr>
                    <tr>
                        <td>特大暴雨</td>
                        <td>&gt; 70.0</td>
                        <td><div class="color-swatch" style="background: #7B043E;"></div></td>
                        <td class="rgb-values">(123, 4, 62)</td>
                        <td class="rgb-values">#7B043E</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 颜色方案对比 -->
        <div class="comparison-grid">
            <!-- 原始颜色方案 -->
            <div class="color-scheme">
                <div class="scheme-title">原始颜色方案</div>
                <div class="color-bar" style="background: linear-gradient(to right, #313695, #4575b4, #74add1, #abd9e9, #fee090, #d73027);"></div>
                <div class="color-details">
                    <div class="color-item">
                        <div class="color-swatch" style="background: #313695;"></div>
                        <span>深蓝 #313695</span>
                    </div>
                    <div class="color-item">
                        <div class="color-swatch" style="background: #4575b4;"></div>
                        <span>蓝色 #4575b4</span>
                    </div>
                    <div class="color-item">
                        <div class="color-swatch" style="background: #74add1;"></div>
                        <span>浅蓝 #74add1</span>
                    </div>
                    <div class="color-item">
                        <div class="color-swatch" style="background: #abd9e9;"></div>
                        <span>淡蓝 #abd9e9</span>
                    </div>
                    <div class="color-item">
                        <div class="color-swatch" style="background: #fee090;"></div>
                        <span>黄色 #fee090</span>
                    </div>
                    <div class="color-item">
                        <div class="color-swatch" style="background: #d73027;"></div>
                        <span>红色 #d73027</span>
                    </div>
                </div>
                <p style="font-size: 12px; color: #666; margin-top: 15px;">
                    <strong>特点：</strong>通用科学可视化配色，蓝-黄-红渐变，适合一般数据展示
                </p>
            </div>

            <!-- 中国气象标准颜色方案 -->
            <div class="color-scheme">
                <div class="scheme-title">中国气象标准颜色方案</div>
                <div class="color-bar" style="background: linear-gradient(to right, #FFFFFF, #9FEF8C, #3BBA3B, #5FBCFD, #0100FB, #F801F5, #7B043E); border: 1px solid #ccc;"></div>
                <div class="color-details">
                    <div class="color-item">
                        <div class="color-swatch" style="background: #FFFFFF; border: 1px solid #ccc;"></div>
                        <span>白色 #FFFFFF (无降雨)</span>
                    </div>
                    <div class="color-item">
                        <div class="color-swatch" style="background: #9FEF8C;"></div>
                        <span>浅绿 #9FEF8C (小雨)</span>
                    </div>
                    <div class="color-item">
                        <div class="color-swatch" style="background: #3BBA3B;"></div>
                        <span>深绿 #3BBA3B (中雨)</span>
                    </div>
                    <div class="color-item">
                        <div class="color-swatch" style="background: #5FBCFD;"></div>
                        <span>浅蓝 #5FBCFD (大雨)</span>
                    </div>
                    <div class="color-item">
                        <div class="color-swatch" style="background: #0100FB;"></div>
                        <span>深蓝 #0100FB (暴雨)</span>
                    </div>
                    <div class="color-item">
                        <div class="color-swatch" style="background: #F801F5;"></div>
                        <span>紫红 #F801F5 (大暴雨)</span>
                    </div>
                    <div class="color-item">
                        <div class="color-swatch" style="background: #7B043E;"></div>
                        <span>深红 #7B043E (特大暴雨)</span>
                    </div>
                </div>
                <p style="font-size: 12px; color: #666; margin-top: 15px;">
                    <strong>特点：</strong>符合中国气象局标准，绿-蓝-紫-红渐变，专业气象应用配色
                </p>
            </div>
        </div>

        <!-- 优势说明 -->
        <div class="standards-section">
            <h2>中国气象标准颜色方案的优势</h2>
            <ul style="line-height: 1.8;">
                <li><strong>权威性：</strong>符合中国气象局官方标准，与天气预报、气象雷达图保持一致</li>
                <li><strong>专业性：</strong>颜色分级与降雨强度等级精确对应，便于专业人员识别</li>
                <li><strong>直观性：</strong>绿色表示轻度降雨，蓝色表示中等降雨，紫红色表示强降雨，符合直觉</li>
                <li><strong>可访问性：</strong>考虑了色盲用户的需求，颜色对比度适中</li>
                <li><strong>一致性：</strong>与国内其他气象可视化系统保持统一，减少用户学习成本</li>
            </ul>
        </div>

        <!-- 实施说明 -->
        <div class="standards-section">
            <h2>实施说明</h2>
            <p><strong>热力图配置：</strong></p>
            <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px;">
gradient: {
    0.0: '#FFFFFF',    // 白色 - 无降雨
    0.1: '#9FEF8C',    // 浅绿 - 小雨 (159, 239, 140)
    0.2: '#3BBA3B',    // 深绿 - 中雨 (59, 186, 59)
    0.4: '#5FBCFD',    // 浅蓝 - 大雨 (95, 188, 253)
    0.6: '#0100FB',    // 深蓝 - 暴雨 (1, 0, 251)
    0.8: '#F801F5',    // 紫红 - 大暴雨 (248, 1, 245)
    1.0: '#7B043E'     // 深红 - 特大暴雨 (123, 4, 62)
}</pre>
            
            <p><strong>CSS渐变配置：</strong></p>
            <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px;">
background: linear-gradient(to right, 
    #FFFFFF, #9FEF8C, #3BBA3B, #5FBCFD, 
    #0100FB, #F801F5, #7B043E
);</pre>
        </div>
    </div>
</body>
</html>
