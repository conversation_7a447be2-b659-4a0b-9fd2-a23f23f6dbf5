#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
气象降雨预报数据处理模块
处理CSV网格数据并转换为地理坐标系统
"""

import pandas as pd
import numpy as np
import json
from typing import List, Dict, Tuple, Any


class RainfallDataProcessor:
    """降雨数据处理器"""
    
    def __init__(self, csv_file_path: str):
        """
        初始化数据处理器
        
        Args:
            csv_file_path: CSV文件路径
        """
        self.csv_file_path = csv_file_path
        
        # 坐标系统参数
        self.lat_min = 33.5  # 最小纬度
        self.lat_max = 35.0  # 最大纬度
        self.lon_min = 107.5  # 最小经度
        self.lon_max = 110.0  # 最大经度
        self.grid_spacing = 0.025  # 网格间距
        
        # 计算网格尺寸
        self.lat_points = int((self.lat_max - self.lat_min) / self.grid_spacing) + 1  # 61个点
        self.lon_points = int((self.lon_max - self.lon_min) / self.grid_spacing) + 1  # 101个点
        
        self.rainfall_data = None
        self.processed_data = None
    
    def load_csv_data(self) -> np.ndarray:
        """
        加载CSV数据
        
        Returns:
            numpy数组格式的降雨数据
        """
        try:
            # 读取CSV文件，不包含表头
            df = pd.read_csv(self.csv_file_path, header=None)
            self.rainfall_data = df.values
            
            print(f"成功加载数据: {self.rainfall_data.shape[0]} 行 × {self.rainfall_data.shape[1]} 列")
            print(f"数据范围: {np.min(self.rainfall_data):.2f} - {np.max(self.rainfall_data):.2f}")
            
            return self.rainfall_data
            
        except Exception as e:
            print(f"加载CSV数据时出错: {e}")
            return None
    
    def generate_coordinates(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        生成经纬度坐标网格
        
        Returns:
            经度和纬度的网格数组
        """
        # 生成经纬度数组
        latitudes = np.linspace(self.lat_max, self.lat_min, self.lat_points)  # 从北到南
        longitudes = np.linspace(self.lon_min, self.lon_max, self.lon_points)  # 从西到东
        
        # 创建网格
        lon_grid, lat_grid = np.meshgrid(longitudes, latitudes)
        
        return lon_grid, lat_grid
    
    def process_data_for_visualization(self) -> List[Dict[str, Any]]:
        """
        处理数据用于可视化
        
        Returns:
            包含坐标和降雨值的字典列表
        """
        if self.rainfall_data is None:
            self.load_csv_data()
        
        if self.rainfall_data is None:
            return []
        
        lon_grid, lat_grid = self.generate_coordinates()
        
        # 将数据转换为点列表
        points = []
        for i in range(self.rainfall_data.shape[0]):
            for j in range(self.rainfall_data.shape[1]):
                points.append({
                    'lat': float(lat_grid[i, j]),
                    'lon': float(lon_grid[i, j]),
                    'rainfall': float(self.rainfall_data[i, j]),
                    'row': i,
                    'col': j
                })
        
        self.processed_data = points
        return points
    
    def get_geojson_data(self) -> Dict[str, Any]:
        """
        生成GeoJSON格式的数据
        
        Returns:
            GeoJSON格式的数据
        """
        if self.processed_data is None:
            self.process_data_for_visualization()
        
        features = []
        for point in self.processed_data:
            feature = {
                "type": "Feature",
                "geometry": {
                    "type": "Point",
                    "coordinates": [point['lon'], point['lat']]
                },
                "properties": {
                    "rainfall": point['rainfall'],
                    "row": point['row'],
                    "col": point['col']
                }
            }
            features.append(feature)
        
        geojson = {
            "type": "FeatureCollection",
            "features": features
        }
        
        return geojson
    
    def get_heatmap_data(self, min_threshold: float = 0.01, normalize: bool = False) -> List[List[float]]:
        """
        获取热力图数据格式

        Args:
            min_threshold: 最小降雨量阈值，低于此值的点将被过滤
            normalize: 是否对数据进行归一化处理以增强低值区域的可见性

        Returns:
            [lat, lon, intensity] 格式的数据列表
        """
        if self.processed_data is None:
            self.process_data_for_visualization()

        heatmap_data = []
        for point in self.processed_data:
            # 过滤掉降雨量很小的点以提高性能，但保留更多数据
            if point['rainfall'] > min_threshold:
                intensity = point['rainfall']

                # 可选的归一化处理，增强低值的可见性
                if normalize and self.rainfall_data is not None:
                    max_val = np.max(self.rainfall_data)
                    min_val = np.min(self.rainfall_data[self.rainfall_data > 0])
                    if max_val > min_val:
                        # 使用对数缩放增强低值可见性
                        intensity = np.log1p(intensity) / np.log1p(max_val)

                heatmap_data.append([
                    point['lat'],
                    point['lon'],
                    intensity
                ])

        return heatmap_data

    def get_adaptive_heatmap_config(self) -> Dict[str, Any]:
        """
        获取自适应的热力图配置

        Returns:
            适合当前数据分布的热力图配置
        """
        if self.rainfall_data is None:
            self.load_csv_data()

        stats = self.get_statistics()

        # 根据数据分布调整配置
        config = {
            'radius': 20,  # 增加半径以覆盖更大区域
            'blur': 10,    # 减少模糊以保持清晰度
            'maxZoom': 17,
            'max': stats['max_rainfall'],
            'minOpacity': 0.1,  # 设置最小透明度，确保低值可见
            'gradient': self.get_china_meteorological_gradient()
        }

        return config
    
    def get_grid_geojson(self) -> Dict[str, Any]:
        """
        生成网格的GeoJSON数据

        Returns:
            网格的GeoJSON格式数据
        """
        if self.rainfall_data is None:
            self.load_csv_data()

        if self.rainfall_data is None:
            return {}

        features = []

        # 为每个网格单元创建矩形
        for i in range(self.lat_points):
            for j in range(self.lon_points):
                # 计算网格单元的四个角点
                lat_top = self.lat_max - i * self.grid_spacing
                lat_bottom = self.lat_max - (i + 1) * self.grid_spacing
                lon_left = self.lon_min + j * self.grid_spacing
                lon_right = self.lon_min + (j + 1) * self.grid_spacing

                # 创建矩形坐标
                coordinates = [[
                    [lon_left, lat_top],
                    [lon_right, lat_top],
                    [lon_right, lat_bottom],
                    [lon_left, lat_bottom],
                    [lon_left, lat_top]  # 闭合多边形
                ]]

                # 获取降雨值
                rainfall_value = float(self.rainfall_data[i, j])

                feature = {
                    "type": "Feature",
                    "geometry": {
                        "type": "Polygon",
                        "coordinates": coordinates
                    },
                    "properties": {
                        "rainfall": rainfall_value,
                        "row": i,
                        "col": j,
                        "lat_center": float(lat_top - self.grid_spacing / 2),
                        "lon_center": float(lon_left + self.grid_spacing / 2)
                    }
                }
                features.append(feature)

        geojson = {
            "type": "FeatureCollection",
            "features": features
        }

        return geojson

    def get_grid_labels_data(self) -> List[Dict[str, Any]]:
        """
        获取网格标签数据（用于显示数值）

        Returns:
            包含标签位置和数值的列表
        """
        if self.rainfall_data is None:
            self.load_csv_data()

        if self.rainfall_data is None:
            return []

        labels = []

        for i in range(self.lat_points):
            for j in range(self.lon_points):
                # 计算网格中心点
                lat_center = self.lat_max - i * self.grid_spacing - self.grid_spacing / 2
                lon_center = self.lon_min + j * self.grid_spacing + self.grid_spacing / 2

                rainfall_value = float(self.rainfall_data[i, j])

                # 只为有意义的降雨值创建标签（避免显示过多的0值）
                if rainfall_value > 0.05:  # 阈值可调整
                    labels.append({
                        'lat': lat_center,
                        'lon': lon_center,
                        'rainfall': rainfall_value,
                        'row': i,
                        'col': j,
                        'text': f"{rainfall_value:.1f}",
                        'intensity_level': self.get_rainfall_intensity_level(rainfall_value),
                        'color': self.get_rainfall_color(rainfall_value)
                    })

        return labels

    def get_rainfall_intensity_level(self, rainfall: float) -> str:
        """
        根据降雨量获取强度等级

        Args:
            rainfall: 降雨量 (mm/h)

        Returns:
            降雨强度等级描述
        """
        if rainfall < 0.1:
            return "无降雨"
        elif rainfall < 2.5:
            return "小雨"
        elif rainfall < 8.0:
            return "中雨"
        elif rainfall < 15.9:
            return "大雨"
        elif rainfall < 30.0:
            return "暴雨"
        elif rainfall < 70.0:
            return "大暴雨"
        else:
            return "特大暴雨"

    def get_rainfall_color(self, rainfall: float) -> str:
        """
        根据降雨量获取对应的中国气象标准颜色

        Args:
            rainfall: 降雨量 (mm/h)

        Returns:
            十六进制颜色代码
        """
        if rainfall < 0.1:
            return "#FFFFFF"  # 白色 - 无降雨
        elif rainfall < 2.5:
            return "#9FEF8C"  # 浅绿 (159, 239, 140)
        elif rainfall < 8.0:
            return "#3BBA3B"  # 深绿 (59, 186, 59)
        elif rainfall < 15.9:
            return "#5FBCFD"  # 浅蓝 (95, 188, 253)
        elif rainfall < 30.0:
            return "#0100FB"  # 深蓝 (1, 0, 251)
        elif rainfall < 70.0:
            return "#F801F5"  # 紫红 (248, 1, 245)
        else:
            return "#7B043E"  # 深红 (123, 4, 62)

    def get_china_meteorological_gradient(self) -> Dict[str, str]:
        """
        获取中国气象标准的颜色渐变配置

        Returns:
            适用于热力图的颜色渐变字典
        """
        return {
            0.0: '#FFFFFF',    # 白色 - 无降雨
            0.1: '#9FEF8C',    # 浅绿 - 小雨
            0.2: '#3BBA3B',    # 深绿 - 中雨
            0.4: '#5FBCFD',    # 浅蓝 - 大雨
            0.6: '#0100FB',    # 深蓝 - 暴雨
            0.8: '#F801F5',    # 紫红 - 大暴雨
            1.0: '#7B043E'     # 深红 - 特大暴雨
        }

    def get_statistics(self) -> Dict[str, float]:
        """
        获取数据统计信息

        Returns:
            统计信息字典
        """
        if self.rainfall_data is None:
            self.load_csv_data()

        if self.rainfall_data is None:
            return {}

        stats = {
            'min_rainfall': float(np.min(self.rainfall_data)),
            'max_rainfall': float(np.max(self.rainfall_data)),
            'mean_rainfall': float(np.mean(self.rainfall_data)),
            'std_rainfall': float(np.std(self.rainfall_data)),
            'total_points': int(self.rainfall_data.size),
            'non_zero_points': int(np.count_nonzero(self.rainfall_data)),
            'lat_range': [self.lat_min, self.lat_max],
            'lon_range': [self.lon_min, self.lon_max],
            'grid_spacing': self.grid_spacing,
            'grid_dimensions': [self.lat_points, self.lon_points]
        }

        return stats


def main():
    """测试函数"""
    processor = RainfallDataProcessor('2025062420.010.csv')
    
    # 加载数据
    data = processor.load_csv_data()
    if data is not None:
        print("数据加载成功!")
        
        # 获取统计信息
        stats = processor.get_statistics()
        print("数据统计:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # 处理数据
        points = processor.process_data_for_visualization()
        print(f"处理后的数据点数量: {len(points)}")
        
        # 显示前几个数据点
        print("前5个数据点:")
        for i, point in enumerate(points[:5]):
            print(f"  点{i+1}: 纬度={point['lat']:.3f}, 经度={point['lon']:.3f}, 降雨量={point['rainfall']:.2f}")


if __name__ == "__main__":
    main()
