#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细诊断坐标映射问题
"""

from data_processor import RainfallDataProcessor
import numpy as np
import pandas as pd

def diagnose_coordinate_mapping():
    """详细诊断坐标映射问题"""
    print("🔍 开始详细诊断坐标映射问题...")
    
    processor = RainfallDataProcessor('2025062420.010.csv')
    processor.load_csv_data()
    
    print(f"📊 CSV数据基本信息:")
    print(f"   数据形状: {processor.rainfall_data.shape}")
    print(f"   数据范围: {processor.rainfall_data.min():.3f} - {processor.rainfall_data.max():.3f}")
    print(f"   非零值数量: {np.count_nonzero(processor.rainfall_data)}")
    
    # 1. 检查坐标生成逻辑
    print(f"\n🗺️  坐标系统参数:")
    print(f"   纬度范围: {processor.lat_min}° - {processor.lat_max}°")
    print(f"   经度范围: {processor.lon_min}° - {processor.lon_max}°")
    print(f"   网格间距: {processor.grid_spacing}°")
    print(f"   预期网格: {processor.lat_points} × {processor.lon_points}")
    
    # 2. 生成坐标网格并检查
    lon_grid, lat_grid = processor.generate_coordinates()
    print(f"\n📍 实际坐标网格:")
    print(f"   纬度实际范围: {lat_grid.min():.4f}° - {lat_grid.max():.4f}°")
    print(f"   经度实际范围: {lon_grid.min():.4f}° - {lon_grid.max():.4f}°")
    print(f"   网格形状: {lat_grid.shape}")
    
    # 3. 检查四个角点的详细信息
    print(f"\n🔍 四个角点详细检查:")
    corners = [
        (0, 0, "左上角(最北最西)"),
        (0, -1, "右上角(最北最东)"),
        (-1, 0, "左下角(最南最西)"),
        (-1, -1, "右下角(最南最东)")
    ]
    
    for i, j, desc in corners:
        lat = lat_grid[i, j]
        lon = lon_grid[i, j]
        rainfall = processor.rainfall_data[i, j]
        print(f"   {desc}: 行{i if i >= 0 else processor.lat_points + i}, 列{j if j >= 0 else processor.lon_points + j}")
        print(f"      坐标: ({lat:.4f}°N, {lon:.4f}°E)")
        print(f"      降雨: {rainfall:.3f}mm")
    
    # 4. 检查南北分布
    print(f"\n🌍 南北分布分析:")
    
    # 按纬度分段统计
    lat_bands = [
        (34.5, 35.0, "北部"),
        (34.0, 34.5, "中北部"),
        (33.5, 34.0, "南部")
    ]
    
    for lat_min, lat_max, region in lat_bands:
        # 找到对应的行范围
        mask = (lat_grid >= lat_min) & (lat_grid < lat_max)
        region_data = processor.rainfall_data[mask]
        non_zero_count = np.count_nonzero(region_data)
        total_count = len(region_data)
        
        print(f"   {region} ({lat_min}°-{lat_max}°N):")
        print(f"      数据点: {total_count}")
        print(f"      非零点: {non_zero_count} ({non_zero_count/total_count*100:.1f}%)")
        print(f"      数值范围: {region_data.min():.3f} - {region_data.max():.3f}")
    
    # 5. 检查热力图数据生成
    print(f"\n🔥 热力图数据生成分析:")
    
    # 使用不同阈值生成热力图数据
    thresholds = [0.0, 0.01, 0.05, 0.1]
    
    for threshold in thresholds:
        heatmap_data = processor.get_heatmap_data(min_threshold=threshold)
        
        if heatmap_data:
            lats = [point[0] for point in heatmap_data]
            lons = [point[1] for point in heatmap_data]
            
            print(f"   阈值 {threshold}: {len(heatmap_data)} 个点")
            print(f"      纬度范围: {min(lats):.4f}° - {max(lats):.4f}°")
            print(f"      经度范围: {min(lons):.4f}° - {max(lons):.4f}°")
            
            # 检查南北分布
            north_count = sum(1 for lat in lats if lat >= 34.25)
            south_count = sum(1 for lat in lats if lat < 34.25)
            print(f"      北部点数: {north_count}, 南部点数: {south_count}")
        else:
            print(f"   阈值 {threshold}: 无数据点")
    
    # 6. 详细检查坐标计算公式
    print(f"\n🧮 坐标计算公式验证:")
    print(f"   纬度计算: lat = lat_max - row * grid_spacing")
    print(f"   经度计算: lon = lon_min + col * grid_spacing")
    
    # 验证几个关键行的纬度
    key_rows = [0, 15, 30, 45, 60]
    for row in key_rows:
        if row < processor.lat_points:
            calculated_lat = processor.lat_max - row * processor.grid_spacing
            actual_lat = lat_grid[row, 0]
            print(f"   行{row}: 计算值={calculated_lat:.4f}°, 实际值={actual_lat:.4f}°, 差异={abs(calculated_lat-actual_lat):.6f}°")
    
    # 7. 生成详细的数据分布图
    print(f"\n📊 生成数据分布分析...")
    
    # 按行统计降雨数据
    row_stats = []
    for i in range(processor.lat_points):
        row_data = processor.rainfall_data[i, :]
        lat = processor.lat_max - i * processor.grid_spacing
        non_zero = np.count_nonzero(row_data)
        max_val = np.max(row_data)
        mean_val = np.mean(row_data)
        
        row_stats.append({
            'row': i,
            'lat': lat,
            'non_zero_count': non_zero,
            'max_rainfall': max_val,
            'mean_rainfall': mean_val
        })
    
    print(f"   前10行统计:")
    for i in range(min(10, len(row_stats))):
        stat = row_stats[i]
        print(f"   行{stat['row']:2d} ({stat['lat']:6.3f}°N): 非零={stat['non_zero_count']:3d}, 最大={stat['max_rainfall']:.3f}, 平均={stat['mean_rainfall']:.3f}")
    
    print(f"   后10行统计:")
    for i in range(max(0, len(row_stats)-10), len(row_stats)):
        stat = row_stats[i]
        print(f"   行{stat['row']:2d} ({stat['lat']:6.3f}°N): 非零={stat['non_zero_count']:3d}, 最大={stat['max_rainfall']:.3f}, 平均={stat['mean_rainfall']:.3f}")
    
    return row_stats

def check_leaflet_data_format():
    """检查Leaflet热力图数据格式"""
    print(f"\n🍃 Leaflet热力图数据格式检查:")
    
    processor = RainfallDataProcessor('2025062420.010.csv')
    processor.load_csv_data()
    
    # 获取热力图数据
    heatmap_data = processor.get_heatmap_data(min_threshold=0.01)
    
    print(f"   数据格式: [lat, lon, intensity]")
    print(f"   数据点数: {len(heatmap_data)}")
    
    if heatmap_data:
        print(f"   前5个数据点:")
        for i, point in enumerate(heatmap_data[:5]):
            print(f"      点{i+1}: [{point[0]:.4f}, {point[1]:.4f}, {point[2]:.3f}]")
        
        print(f"   后5个数据点:")
        for i, point in enumerate(heatmap_data[-5:], len(heatmap_data)-5):
            print(f"      点{i+1}: [{point[0]:.4f}, {point[1]:.4f}, {point[2]:.3f}]")
        
        # 检查数据是否按纬度排序
        lats = [point[0] for point in heatmap_data]
        is_sorted_desc = all(lats[i] >= lats[i+1] for i in range(len(lats)-1))
        is_sorted_asc = all(lats[i] <= lats[i+1] for i in range(len(lats)-1))
        
        print(f"   纬度排序: 降序={is_sorted_desc}, 升序={is_sorted_asc}")
        
        # 检查是否有重复坐标
        coords = [(point[0], point[1]) for point in heatmap_data]
        unique_coords = set(coords)
        print(f"   坐标唯一性: {len(coords)} 总点, {len(unique_coords)} 唯一点")

def generate_debug_html():
    """生成调试用的HTML页面"""
    processor = RainfallDataProcessor('2025062420.010.csv')
    processor.load_csv_data()
    
    # 获取所有数据点（不过滤）
    all_points = processor.process_data_for_visualization()
    heatmap_data = processor.get_heatmap_data(min_threshold=0.0)
    
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>坐标映射调试</title>
        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
        <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
        <script src="https://unpkg.com/leaflet.heat@0.2.0/dist/leaflet-heat.js"></script>
        <style>
            body {{ margin: 0; padding: 0; font-family: Arial, sans-serif; }}
            #map {{ height: 70vh; width: 100%; }}
            .info {{ padding: 20px; background: #f5f5f5; }}
        </style>
    </head>
    <body>
        <div id="map"></div>
        <div class="info">
            <h3>坐标映射调试信息</h3>
            <p>总数据点: {len(all_points)}</p>
            <p>热力图点: {len(heatmap_data)}</p>
            <p>数据范围: {processor.rainfall_data.min():.3f} - {processor.rainfall_data.max():.3f}</p>
            <p>预期坐标范围: 纬度 {processor.lat_min}° - {processor.lat_max}°, 经度 {processor.lon_min}° - {processor.lon_max}°</p>
        </div>
        
        <script>
            const map = L.map('map').setView([34.25, 108.75], 8);
            
            L.tileLayer('https://{{s}}.tile.openstreetmap.org/{{z}}/{{x}}/{{y}}.png', {{
                attribution: '© OpenStreetMap contributors'
            }}).addTo(map);
            
            // 添加边界框
            const bounds = [
                [{processor.lat_min}, {processor.lon_min}],
                [{processor.lat_max}, {processor.lon_max}]
            ];
            L.rectangle(bounds, {{color: 'red', weight: 2, fillOpacity: 0}}).addTo(map);
            
            // 添加所有数据点
            const allData = {str(heatmap_data)};
            
            // 添加热力图
            if (allData.length > 0) {{
                L.heatLayer(allData, {{
                    radius: 20,
                    blur: 10,
                    maxZoom: 17,
                    max: {processor.rainfall_data.max():.3f},
                    gradient: {{
                        0.0: '#FFFFFF',
                        0.1: '#9FEF8C',
                        0.2: '#3BBA3B',
                        0.4: '#5FBCFD',
                        0.6: '#0100FB',
                        0.8: '#F801F5',
                        1.0: '#7B043E'
                    }}
                }}).addTo(map);
            }}
            
            // 添加角点标记
            const corners = [
                [{processor.lat_max}, {processor.lon_min}, "左上角"],
                [{processor.lat_max}, {processor.lon_max}, "右上角"],
                [{processor.lat_min}, {processor.lon_min}, "左下角"],
                [{processor.lat_min}, {processor.lon_max}, "右下角"]
            ];
            
            corners.forEach(corner => {{
                L.marker([corner[0], corner[1]])
                    .bindPopup(corner[2] + `<br>(${{corner[0]}}°N, ${{corner[1]}}°E)`)
                    .addTo(map);
            }});
        </script>
    </body>
    </html>
    """
    
    with open('debug_coordinates.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ 生成调试页面: debug_coordinates.html")

if __name__ == "__main__":
    row_stats = diagnose_coordinate_mapping()
    check_leaflet_data_format()
    generate_debug_html()
    print(f"\n🎯 诊断完成！请查看生成的调试页面进行可视化验证。")
