<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>气象降雨预报网格可视化系统</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://unpkg.com/leaflet.heat@0.2.0/dist/leaflet-heat.js"></script>
    <style>
        body { margin: 0; padding: 0; font-family: Arial, sans-serif; }
        #map { height: 100vh; width: 100%; }
        .control-panel {
            position: absolute; top: 10px; left: 10px; background: white;
            padding: 15px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000; max-width: 300px;
        }
        .control-panel h3 { margin: 0 0 15px 0; color: #333; font-size: 16px; }
        .control-buttons { margin-bottom: 15px; }
        .control-buttons button {
            background: #007bff; color: white; border: none; padding: 8px 12px;
            margin: 2px; border-radius: 4px; cursor: pointer; font-size: 12px;
        }
        .control-buttons button:hover { background: #0056b3; }
        .control-buttons button.active { background: #28a745; }
        .stats { font-size: 12px; line-height: 1.4; }
        .legend {
            margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px;
        }
        .legend-title { font-size: 12px; font-weight: bold; margin-bottom: 5px; }
        .legend-scale {
            height: 15px; background: linear-gradient(to right, #313695, #4575b4, #74add1, #abd9e9, #fee090, #d73027);
            border-radius: 3px; margin-bottom: 5px;
        }
        .legend-labels { display: flex; justify-content: space-between; font-size: 10px; }
    </style>
</head>
<body>
    <div id="map"></div>
    <div class="control-panel">
        <h3>🌧️ 降雨预报网格数据</h3>
        <div class="control-buttons">
            <button id="toggle-heatmap" class="active">热力图</button>
            <button id="toggle-grid">网格</button>
            <button id="toggle-labels">数值标签</button>
        </div>
        <div id="stats" class="stats">加载中...</div>
        <div class="legend">
            <div class="legend-title">降雨强度 (mm)</div>
            <div class="legend-scale"></div>
            <div class="legend-labels">
                <span id="min-val">0</span>
                <span id="max-val">1</span>
            </div>
        </div>
    </div>

    <script>
        // 初始化地图
        const map = L.map('map').setView([34.25, 108.75], 9);
        
        // 添加底图
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);
        
        // 图层变量
        let heatLayer = null;
        let gridLayer = null;
        let labelsLayer = null;
        
        // 显示状态
        let showHeatmap = true;
        let showGrid = false;
        let showLabels = false;
        
        // 数据变量
        let stats = null;
        
        // 模拟数据（实际应用中从API获取）
        function generateTestData() {
            const data = [];
            const gridData = { type: "FeatureCollection", features: [] };
            const labelsData = [];
            
            // 生成测试数据 - 模拟61x101网格
            const latMin = 33.5, latMax = 35.0;
            const lonMin = 107.5, lonMax = 110.0;
            const latPoints = 61, lonPoints = 101;
            const spacing = 0.025;
            
            for (let i = 0; i < latPoints; i++) {
                for (let j = 0; j < lonPoints; j++) {
                    const lat = latMax - i * spacing;
                    const lon = lonMin + j * spacing;
                    
                    // 模拟降雨数据 - 创建一个有趣的模式
                    const centerLat = 34.25, centerLon = 108.75;
                    const distance = Math.sqrt(Math.pow(lat - centerLat, 2) + Math.pow(lon - centerLon, 2));
                    let rainfall = Math.max(0, 1.2 - distance * 2);
                    rainfall += Math.random() * 0.3; // 添加随机性
                    rainfall = Math.max(0, Math.min(1.5, rainfall));
                    
                    // 热力图数据
                    if (rainfall > 0.05) {
                        data.push([lat, lon, rainfall]);
                    }
                    
                    // 网格数据
                    const latTop = lat + spacing/2;
                    const latBottom = lat - spacing/2;
                    const lonLeft = lon - spacing/2;
                    const lonRight = lon + spacing/2;
                    
                    gridData.features.push({
                        type: "Feature",
                        geometry: {
                            type: "Polygon",
                            coordinates: [[
                                [lonLeft, latTop],
                                [lonRight, latTop],
                                [lonRight, latBottom],
                                [lonLeft, latBottom],
                                [lonLeft, latTop]
                            ]]
                        },
                        properties: {
                            rainfall: rainfall,
                            row: i,
                            col: j,
                            lat_center: lat,
                            lon_center: lon
                        }
                    });
                    
                    // 标签数据
                    if (rainfall > 0.2) {
                        labelsData.push({
                            lat: lat,
                            lon: lon,
                            rainfall: rainfall,
                            text: rainfall.toFixed(1)
                        });
                    }
                }
            }
            
            return { heatmapData: data, gridData: gridData, labelsData: labelsData };
        }
        
        // 初始化数据和图层
        function initializeVisualization() {
            const testData = generateTestData();
            
            // 更新统计信息
            const rainfalls = testData.heatmapData.map(d => d[2]);
            stats = {
                total_points: testData.gridData.features.length,
                heatmap_points: testData.heatmapData.length,
                min_rainfall: Math.min(...rainfalls),
                max_rainfall: Math.max(...rainfalls),
                mean_rainfall: rainfalls.reduce((a, b) => a + b, 0) / rainfalls.length
            };
            
            document.getElementById('stats').innerHTML = `
                <div>网格: 61 × 101</div>
                <div>总数据点: ${stats.total_points.toLocaleString()}</div>
                <div>热力图点: ${stats.heatmap_points.toLocaleString()}</div>
                <div>降雨范围: ${stats.min_rainfall.toFixed(2)} - ${stats.max_rainfall.toFixed(2)} mm</div>
                <div>平均降雨: ${stats.mean_rainfall.toFixed(2)} mm</div>
            `;
            
            document.getElementById('min-val').textContent = stats.min_rainfall.toFixed(1);
            document.getElementById('max-val').textContent = stats.max_rainfall.toFixed(1);
            
            // 创建热力图图层
            heatLayer = L.heatLayer(testData.heatmapData, {
                radius: 25,
                blur: 15,
                maxZoom: 17,
                max: stats.max_rainfall,
                gradient: {
                    0.0: '#313695',
                    0.2: '#4575b4',
                    0.4: '#74add1',
                    0.6: '#abd9e9',
                    0.8: '#fee090',
                    1.0: '#d73027'
                }
            });
            
            // 创建网格图层
            gridLayer = L.geoJSON(testData.gridData, {
                style: {
                    color: '#333333',
                    weight: 1,
                    opacity: 0.7,
                    fillOpacity: 0
                },
                onEachFeature: function(feature, layer) {
                    layer.on('click', function(e) {
                        const props = feature.properties;
                        L.popup()
                            .setLatLng(e.latlng)
                            .setContent(`
                                <div style="font-size: 12px;">
                                    <h4 style="margin: 0 0 8px 0;">网格信息</h4>
                                    <div><strong>降雨量:</strong> ${props.rainfall.toFixed(2)} mm</div>
                                    <div><strong>网格位置:</strong> 行${props.row}, 列${props.col}</div>
                                    <div><strong>中心坐标:</strong> ${props.lat_center.toFixed(4)}°N, ${props.lon_center.toFixed(4)}°E</div>
                                </div>
                            `)
                            .openOn(map);
                    });
                    
                    layer.on('mouseover', function() {
                        layer.setStyle({ weight: 2, color: '#000000' });
                    });
                    
                    layer.on('mouseout', function() {
                        layer.setStyle({ weight: 1, color: '#333333' });
                    });
                }
            });
            
            // 创建标签图层
            labelsLayer = L.layerGroup();
            testData.labelsData.forEach(label => {
                const marker = L.marker([label.lat, label.lon], {
                    icon: L.divIcon({
                        className: 'rainfall-label',
                        html: `<div style="
                            background: rgba(255,255,255,0.9);
                            border: 1px solid #333;
                            border-radius: 3px;
                            padding: 2px 4px;
                            font-size: 10px;
                            font-weight: bold;
                            color: #333;
                            text-align: center;
                        ">${label.text}</div>`,
                        iconSize: [30, 16],
                        iconAnchor: [15, 8]
                    })
                });
                labelsLayer.addLayer(marker);
            });
            
            // 默认显示热力图
            if (showHeatmap) heatLayer.addTo(map);
            
            // 设置控制按钮
            setupControls();
        }
        
        // 设置控制按钮
        function setupControls() {
            document.getElementById('toggle-heatmap').addEventListener('click', function() {
                showHeatmap = !showHeatmap;
                this.classList.toggle('active', showHeatmap);
                if (showHeatmap) heatLayer.addTo(map);
                else map.removeLayer(heatLayer);
            });
            
            document.getElementById('toggle-grid').addEventListener('click', function() {
                showGrid = !showGrid;
                this.classList.toggle('active', showGrid);
                if (showGrid) gridLayer.addTo(map);
                else map.removeLayer(gridLayer);
            });
            
            document.getElementById('toggle-labels').addEventListener('click', function() {
                showLabels = !showLabels;
                this.classList.toggle('active', showLabels);
                if (showLabels) labelsLayer.addTo(map);
                else map.removeLayer(labelsLayer);
            });
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeVisualization();
        });
    </script>
</body>
</html>
