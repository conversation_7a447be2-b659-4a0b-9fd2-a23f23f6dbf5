#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复南部显示问题的应用
"""

from flask import Flask, jsonify, render_template_string
from flask_cors import CORS
import json
import os
from data_processor import RainfallDataProcessor

app = Flask(__name__)
CORS(app)

# 修复后的HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复的气象降雨预报可视化</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://unpkg.com/leaflet.heat@0.2.0/dist/leaflet-heat.js"></script>
    <style>
        body { margin: 0; padding: 0; font-family: Arial, sans-serif; }
        #map { height: 100vh; width: 100%; }
        .info-panel {
            position: absolute; top: 10px; left: 10px; background: white;
            padding: 15px; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000; max-width: 300px;
        }
        .control-buttons { margin-bottom: 15px; }
        .control-buttons button {
            background: #007bff; color: white; border: none; padding: 6px 10px;
            margin: 2px; border-radius: 3px; cursor: pointer; font-size: 11px;
        }
        .control-buttons button:hover { background: #0056b3; }
        .control-buttons button.active { background: #28a745; }
        .slider-container { margin: 8px 0; }
        .slider-container label { display: block; font-size: 11px; margin-bottom: 3px; }
        .slider-container input { width: 100%; }
        .stats { font-size: 11px; line-height: 1.4; }
        .problem-indicator { background: #fff3cd; border: 1px solid #ffeaa7; padding: 8px; border-radius: 4px; margin-bottom: 10px; font-size: 11px; }
    </style>
</head>
<body>
    <div id="map"></div>
    <div class="info-panel">
        <h3>🌧️ 修复的降雨可视化</h3>
        
        <div class="problem-indicator">
            <strong>问题诊断:</strong> 南部数据值过小，需要增强显示
        </div>
        
        <div class="control-buttons">
            <button id="toggle-original" class="active">原始热力图</button>
            <button id="toggle-enhanced">增强热力图</button>
            <button id="toggle-grid">网格</button>
        </div>
        
        <div class="slider-container">
            <label for="min-opacity">最小透明度: <span id="opacity-value">0.1</span></label>
            <input type="range" id="min-opacity" min="0.05" max="0.5" step="0.05" value="0.1">
        </div>
        
        <div class="slider-container">
            <label for="radius-slider">半径: <span id="radius-value">25</span></label>
            <input type="range" id="radius-slider" min="15" max="50" step="5" value="25">
        </div>
        
        <div id="stats" class="stats">加载中...</div>
    </div>

    <script>
        // 初始化地图
        const map = L.map('map').setView([34.25, 108.75], 9);
        
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);
        
        // 图层变量
        let originalHeatLayer = null;
        let enhancedHeatLayer = null;
        let gridLayer = null;
        
        // 显示状态
        let showOriginal = true;
        let showEnhanced = false;
        let showGrid = false;
        
        // 数据变量
        let originalData = null;
        let enhancedData = null;
        let stats = null;
        
        // 加载数据
        Promise.all([
            fetch('/api/fixed/original').then(r => r.json()),
            fetch('/api/fixed/enhanced').then(r => r.json()),
            fetch('/api/rainfall/grid').then(r => r.json())
        ]).then(([originalResponse, enhancedResponse, gridResponse]) => {
            if (originalResponse.success) {
                originalData = originalResponse.data;
                stats = originalResponse.stats;
                
                document.getElementById('stats').innerHTML = `
                    <div><strong>数据分析:</strong></div>
                    <div>总点数: ${stats.total_points}</div>
                    <div>原始范围: ${stats.min_rainfall.toFixed(3)} - ${stats.max_rainfall.toFixed(3)}</div>
                    <div>北部点数: ${originalResponse.north_count}</div>
                    <div>南部点数: ${originalResponse.south_count}</div>
                    <div>南北比例: ${(originalResponse.south_count/originalResponse.north_count).toFixed(2)}</div>
                `;
                
                // 创建原始热力图
                originalHeatLayer = L.heatLayer(originalData, {
                    radius: 25,
                    blur: 15,
                    maxZoom: 17,
                    max: stats.max_rainfall,
                    gradient: {
                        0.0: '#FFFFFF', 0.1: '#9FEF8C', 0.2: '#3BBA3B',
                        0.4: '#5FBCFD', 0.6: '#0100FB', 0.8: '#F801F5', 1.0: '#7B043E'
                    }
                });
                
                if (showOriginal) originalHeatLayer.addTo(map);
            }
            
            if (enhancedResponse.success) {
                enhancedData = enhancedResponse.data;
                updateEnhancedHeatmap();
            }
            
            // 创建网格图层
            if (gridResponse.success) {
                gridLayer = L.geoJSON(gridResponse.grid, {
                    style: { color: '#333333', weight: 1, opacity: 0.6, fillOpacity: 0 },
                    onEachFeature: function(feature, layer) {
                        layer.on('click', function(e) {
                            const props = feature.properties;
                            L.popup()
                                .setLatLng(e.latlng)
                                .setContent(`
                                    <div style="font-size: 12px;">
                                        <h4>网格信息</h4>
                                        <div><strong>降雨量:</strong> ${props.rainfall.toFixed(3)} mm</div>
                                        <div><strong>位置:</strong> 行${props.row}, 列${props.col}</div>
                                        <div><strong>坐标:</strong> ${props.lat_center.toFixed(4)}°N, ${props.lon_center.toFixed(4)}°E</div>
                                    </div>
                                `)
                                .openOn(map);
                        });
                    }
                });
            }
            
            setupControls();
            
            // 添加边界框和标记
            const bounds = [[33.5, 107.5], [35.0, 110.0]];
            L.rectangle(bounds, {color: 'red', weight: 2, fillOpacity: 0}).addTo(map);
            
            // 添加分界线
            L.polyline([[34.25, 107.5], [34.25, 110.0]], {color: 'blue', weight: 2, dashArray: '5, 5'})
                .bindPopup('南北分界线 (34.25°N)')
                .addTo(map);
                
        }).catch(error => {
            document.getElementById('stats').innerHTML = '<p style="color: red;">加载失败: ' + error.message + '</p>';
        });
        
        // 更新增强热力图
        function updateEnhancedHeatmap() {
            if (!enhancedData) return;
            
            const minOpacity = parseFloat(document.getElementById('min-opacity').value);
            const radius = parseInt(document.getElementById('radius-slider').value);
            
            if (enhancedHeatLayer) {
                map.removeLayer(enhancedHeatLayer);
            }
            
            enhancedHeatLayer = L.heatLayer(enhancedData, {
                radius: radius,
                blur: 10,
                maxZoom: 17,
                max: 1.0,
                minOpacity: minOpacity,
                gradient: {
                    0.0: '#FFFFFF', 0.1: '#9FEF8C', 0.2: '#3BBA3B',
                    0.4: '#5FBCFD', 0.6: '#0100FB', 0.8: '#F801F5', 1.0: '#7B043E'
                }
            });
            
            if (showEnhanced) {
                enhancedHeatLayer.addTo(map);
            }
        }
        
        // 设置控制
        function setupControls() {
            document.getElementById('toggle-original').addEventListener('click', function() {
                showOriginal = !showOriginal;
                this.classList.toggle('active', showOriginal);
                if (showOriginal && originalHeatLayer) originalHeatLayer.addTo(map);
                else if (originalHeatLayer) map.removeLayer(originalHeatLayer);
                
                if (showOriginal && showEnhanced) {
                    showEnhanced = false;
                    document.getElementById('toggle-enhanced').classList.remove('active');
                    if (enhancedHeatLayer) map.removeLayer(enhancedHeatLayer);
                }
            });
            
            document.getElementById('toggle-enhanced').addEventListener('click', function() {
                showEnhanced = !showEnhanced;
                this.classList.toggle('active', showEnhanced);
                if (showEnhanced && enhancedHeatLayer) enhancedHeatLayer.addTo(map);
                else if (enhancedHeatLayer) map.removeLayer(enhancedHeatLayer);
                
                if (showOriginal && showEnhanced) {
                    showOriginal = false;
                    document.getElementById('toggle-original').classList.remove('active');
                    if (originalHeatLayer) map.removeLayer(originalHeatLayer);
                }
            });
            
            document.getElementById('toggle-grid').addEventListener('click', function() {
                showGrid = !showGrid;
                this.classList.toggle('active', showGrid);
                if (showGrid && gridLayer) gridLayer.addTo(map);
                else if (gridLayer) map.removeLayer(gridLayer);
            });
            
            // 滑块控制
            document.getElementById('min-opacity').addEventListener('input', function() {
                document.getElementById('opacity-value').textContent = this.value;
                updateEnhancedHeatmap();
            });
            
            document.getElementById('radius-slider').addEventListener('input', function() {
                document.getElementById('radius-value').textContent = this.value;
                updateEnhancedHeatmap();
            });
        }
    </script>
</body>
</html>
"""

# 全局数据处理器
processor = None

def initialize_processor():
    global processor
    csv_file = '2025062420.010.csv'
    if os.path.exists(csv_file):
        processor = RainfallDataProcessor(csv_file)
        processor.load_csv_data()
        print("数据处理器初始化成功")
        return True
    else:
        print(f"错误: 找不到CSV文件 {csv_file}")
        return False

@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/fixed/original')
def get_original_data():
    try:
        if processor is None:
            return jsonify({'success': False, 'error': '数据处理器未初始化'})
        
        stats = processor.get_statistics()
        heatmap_data = processor.get_heatmap_data(min_threshold=0.01)
        
        # 分析南北分布
        north_count = sum(1 for point in heatmap_data if point[0] >= 34.25)
        south_count = sum(1 for point in heatmap_data if point[0] < 34.25)
        
        return jsonify({
            'success': True,
            'data': heatmap_data,
            'stats': stats,
            'north_count': north_count,
            'south_count': south_count
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/fixed/enhanced')
def get_enhanced_data():
    try:
        if processor is None:
            return jsonify({'success': False, 'error': '数据处理器未初始化'})
        
        # 获取归一化的热力图数据
        enhanced_data = processor.get_heatmap_data(min_threshold=0.01, normalize=True)
        
        return jsonify({
            'success': True,
            'data': enhanced_data
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/rainfall/grid')
def get_grid_data():
    try:
        if processor is None:
            return jsonify({'success': False, 'error': '数据处理器未初始化'})
        
        grid_geojson = processor.get_grid_geojson()
        
        return jsonify({
            'success': True,
            'grid': grid_geojson
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    print("启动修复版本的测试服务器...")
    if initialize_processor():
        print("访问 http://localhost:5000 查看修复后的地图")
        print("功能说明:")
        print("  - 原始热力图: 显示真实数据分布")
        print("  - 增强热力图: 使用对数缩放增强低值可见性")
        print("  - 网格显示: 显示精确的数据网格")
        print("  - 可调参数: 最小透明度和半径")
        app.run(debug=True, host='0.0.0.0', port=5000)
    else:
        print("初始化失败")
