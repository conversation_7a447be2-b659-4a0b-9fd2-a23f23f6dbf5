<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>改进的降雨可视化 - 解决南部显示问题</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://unpkg.com/leaflet.heat@0.2.0/dist/leaflet-heat.js"></script>
    <style>
        body { margin: 0; padding: 0; font-family: Arial, sans-serif; }
        #map { height: 100vh; width: 100%; }
        .control-panel {
            position: absolute; top: 10px; left: 10px; background: white;
            padding: 15px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000; max-width: 320px;
        }
        .control-panel h3 { margin: 0 0 15px 0; color: #333; font-size: 16px; }
        .control-buttons { margin-bottom: 15px; }
        .control-buttons button {
            background: #007bff; color: white; border: none; padding: 8px 12px;
            margin: 2px; border-radius: 4px; cursor: pointer; font-size: 12px;
        }
        .control-buttons button:hover { background: #0056b3; }
        .control-buttons button.active { background: #28a745; }
        .slider-container { margin: 10px 0; }
        .slider-container label { display: block; font-size: 12px; margin-bottom: 5px; }
        .slider-container input { width: 100%; }
        .stats { font-size: 12px; line-height: 1.4; }
        .region-stats { margin-top: 10px; font-size: 11px; }
        .region-stats div { margin: 2px 0; }
    </style>
</head>
<body>
    <div id="map"></div>
    <div class="control-panel">
        <h3>🌧️ 改进的降雨可视化</h3>
        
        <div class="control-buttons">
            <button id="toggle-original" class="active">原始热力图</button>
            <button id="toggle-enhanced">增强热力图</button>
            <button id="toggle-grid">网格</button>
        </div>
        
        <div class="slider-container">
            <label for="intensity-slider">强度增强: <span id="intensity-value">1.0</span></label>
            <input type="range" id="intensity-slider" min="0.5" max="5.0" step="0.1" value="1.0">
        </div>
        
        <div class="slider-container">
            <label for="radius-slider">热力图半径: <span id="radius-value">20</span></label>
            <input type="range" id="radius-slider" min="10" max="50" step="5" value="20">
        </div>
        
        <div id="stats" class="stats">加载中...</div>
        
        <div class="region-stats">
            <div><strong>区域分布:</strong></div>
            <div id="region-stats">分析中...</div>
        </div>
    </div>

    <script>
        // 初始化地图
        const map = L.map('map').setView([34.25, 108.75], 9);
        
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);
        
        // 图层变量
        let originalHeatLayer = null;
        let enhancedHeatLayer = null;
        let gridLayer = null;
        
        // 显示状态
        let showOriginal = true;
        let showEnhanced = false;
        let showGrid = false;
        
        // 数据变量
        let allData = null;
        let gridData = null;
        let stats = null;
        
        // 生成测试数据（模拟实际CSV数据的分布特征）
        function generateRealisticData() {
            const data = [];
            const gridDataFeatures = [];
            
            // 模拟实际数据的南北分布特征
            const latMin = 33.5, latMax = 35.0;
            const lonMin = 107.5, lonMax = 110.0;
            const latPoints = 61, lonPoints = 101;
            const spacing = 0.025;
            
            for (let i = 0; i < latPoints; i++) {
                for (let j = 0; j < lonPoints; j++) {
                    const lat = latMax - i * spacing;
                    const lon = lonMin + j * spacing;
                    
                    // 模拟北部高降雨、南部低降雨的分布
                    let rainfall;
                    if (lat >= 34.5) {
                        // 北部：高降雨区域
                        rainfall = 0.6 + Math.random() * 0.5; // 0.6-1.1
                    } else if (lat >= 34.0) {
                        // 中部：中等降雨
                        rainfall = Math.random() < 0.7 ? 0.2 + Math.random() * 0.6 : 0; // 0-0.8
                    } else {
                        // 南部：低降雨区域
                        rainfall = Math.random() < 0.7 ? Math.random() * 0.2 : 0; // 0-0.2
                    }
                    
                    if (rainfall > 0.01) {
                        data.push([lat, lon, rainfall]);
                    }
                    
                    // 网格数据
                    const latTop = lat + spacing/2;
                    const latBottom = lat - spacing/2;
                    const lonLeft = lon - spacing/2;
                    const lonRight = lon + spacing/2;
                    
                    gridDataFeatures.push({
                        type: "Feature",
                        geometry: {
                            type: "Polygon",
                            coordinates: [[
                                [lonLeft, latTop], [lonRight, latTop],
                                [lonRight, latBottom], [lonLeft, latBottom],
                                [lonLeft, latTop]
                            ]]
                        },
                        properties: { rainfall: rainfall, row: i, col: j }
                    });
                }
            }
            
            return {
                heatmapData: data,
                gridData: { type: "FeatureCollection", features: gridDataFeatures }
            };
        }
        
        // 创建增强的热力图数据
        function createEnhancedData(originalData, intensityMultiplier = 2.0) {
            return originalData.map(point => {
                const [lat, lon, intensity] = point;
                // 对低值进行对数增强
                const enhanced = Math.log1p(intensity * intensityMultiplier) / Math.log1p(intensityMultiplier);
                return [lat, lon, Math.max(0.1, enhanced)]; // 确保最小可见值
            });
        }
        
        // 初始化可视化
        function initializeVisualization() {
            const testData = generateRealisticData();
            allData = testData.heatmapData;
            gridData = testData.gridData;
            
            // 计算统计信息
            const rainfalls = allData.map(d => d[2]);
            stats = {
                total_points: allData.length,
                min_rainfall: Math.min(...rainfalls),
                max_rainfall: Math.max(...rainfalls),
                mean_rainfall: rainfalls.reduce((a, b) => a + b, 0) / rainfalls.length
            };
            
            // 分析区域分布
            const northData = allData.filter(d => d[0] >= 34.25);
            const southData = allData.filter(d => d[0] < 34.25);
            
            document.getElementById('stats').innerHTML = `
                <div>总数据点: ${stats.total_points.toLocaleString()}</div>
                <div>降雨范围: ${stats.min_rainfall.toFixed(3)} - ${stats.max_rainfall.toFixed(3)} mm</div>
                <div>平均降雨: ${stats.mean_rainfall.toFixed(3)} mm</div>
            `;
            
            document.getElementById('region-stats').innerHTML = `
                <div>北部 (≥34.25°): ${northData.length} 点</div>
                <div>南部 (<34.25°): ${southData.length} 点</div>
                <div>南北比例: ${(southData.length/northData.length).toFixed(2)}</div>
            `;
            
            // 创建原始热力图
            originalHeatLayer = L.heatLayer(allData, {
                radius: 20,
                blur: 15,
                maxZoom: 17,
                max: stats.max_rainfall,
                gradient: {
                    0.0: '#FFFFFF', 0.1: '#9FEF8C', 0.2: '#3BBA3B',
                    0.4: '#5FBCFD', 0.6: '#0100FB', 0.8: '#F801F5', 1.0: '#7B043E'
                }
            });
            
            // 创建增强热力图
            updateEnhancedHeatmap();
            
            // 创建网格图层
            gridLayer = L.geoJSON(gridData, {
                style: { color: '#333333', weight: 1, opacity: 0.6, fillOpacity: 0 },
                onEachFeature: function(feature, layer) {
                    layer.on('click', function(e) {
                        const props = feature.properties;
                        L.popup()
                            .setLatLng(e.latlng)
                            .setContent(`
                                <div style="font-size: 12px;">
                                    <h4 style="margin: 0 0 8px 0;">网格信息</h4>
                                    <div><strong>降雨量:</strong> ${props.rainfall.toFixed(3)} mm</div>
                                    <div><strong>位置:</strong> 行${props.row}, 列${props.col}</div>
                                </div>
                            `)
                            .openOn(map);
                    });
                }
            });
            
            // 默认显示原始热力图
            if (showOriginal) originalHeatLayer.addTo(map);
            
            // 设置控制
            setupControls();
            
            // 添加边界框
            const bounds = [[33.5, 107.5], [35.0, 110.0]];
            L.rectangle(bounds, {color: 'red', weight: 2, fillOpacity: 0}).addTo(map);
        }
        
        // 更新增强热力图
        function updateEnhancedHeatmap() {
            const intensityMultiplier = parseFloat(document.getElementById('intensity-slider').value);
            const radius = parseInt(document.getElementById('radius-slider').value);
            
            const enhancedData = createEnhancedData(allData, intensityMultiplier);
            
            if (enhancedHeatLayer) {
                map.removeLayer(enhancedHeatLayer);
            }
            
            enhancedHeatLayer = L.heatLayer(enhancedData, {
                radius: radius,
                blur: 10,
                maxZoom: 17,
                max: 1.0, // 归一化后的最大值
                minOpacity: 0.2, // 确保低值可见
                gradient: {
                    0.0: '#FFFFFF', 0.1: '#9FEF8C', 0.2: '#3BBA3B',
                    0.4: '#5FBCFD', 0.6: '#0100FB', 0.8: '#F801F5', 1.0: '#7B043E'
                }
            });
            
            if (showEnhanced) {
                enhancedHeatLayer.addTo(map);
            }
        }
        
        // 设置控制
        function setupControls() {
            document.getElementById('toggle-original').addEventListener('click', function() {
                showOriginal = !showOriginal;
                this.classList.toggle('active', showOriginal);
                if (showOriginal) originalHeatLayer.addTo(map);
                else map.removeLayer(originalHeatLayer);
                
                if (showOriginal && showEnhanced) {
                    showEnhanced = false;
                    document.getElementById('toggle-enhanced').classList.remove('active');
                    map.removeLayer(enhancedHeatLayer);
                }
            });
            
            document.getElementById('toggle-enhanced').addEventListener('click', function() {
                showEnhanced = !showEnhanced;
                this.classList.toggle('active', showEnhanced);
                if (showEnhanced) enhancedHeatLayer.addTo(map);
                else map.removeLayer(enhancedHeatLayer);
                
                if (showOriginal && showEnhanced) {
                    showOriginal = false;
                    document.getElementById('toggle-original').classList.remove('active');
                    map.removeLayer(originalHeatLayer);
                }
            });
            
            document.getElementById('toggle-grid').addEventListener('click', function() {
                showGrid = !showGrid;
                this.classList.toggle('active', showGrid);
                if (showGrid) gridLayer.addTo(map);
                else map.removeLayer(gridLayer);
            });
            
            // 滑块控制
            document.getElementById('intensity-slider').addEventListener('input', function() {
                document.getElementById('intensity-value').textContent = this.value;
                updateEnhancedHeatmap();
            });
            
            document.getElementById('radius-slider').addEventListener('input', function() {
                document.getElementById('radius-value').textContent = this.value;
                updateEnhancedHeatmap();
            });
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeVisualization();
        });
    </script>
</body>
</html>
