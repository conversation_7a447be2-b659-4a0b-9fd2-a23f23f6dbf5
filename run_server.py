#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
气象降雨预报可视化系统启动脚本
"""

import os
import sys
import subprocess
import webbrowser
import time
from threading import Timer

def check_dependencies():
    """检查依赖包是否安装"""
    required_packages = ['pandas', 'numpy', 'flask', 'flask_cors']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def check_data_file():
    """检查数据文件是否存在"""
    data_file = '2025062420.010.csv'
    if not os.path.exists(data_file):
        print(f"错误: 找不到数据文件 {data_file}")
        print("请确保CSV文件在当前目录中")
        return False
    return True

def open_browser():
    """延迟打开浏览器"""
    time.sleep(2)  # 等待服务器启动
    webbrowser.open('http://localhost:5000')

def main():
    """主函数"""
    print("=" * 50)
    print("🌧️  气象降雨预报可视化系统")
    print("=" * 50)
    
    # 检查依赖
    print("检查依赖包...")
    if not check_dependencies():
        return
    
    # 检查数据文件
    print("检查数据文件...")
    if not check_data_file():
        return
    
    print("✅ 所有检查通过!")
    print("\n启动Web服务器...")
    print("📍 访问地址: http://localhost:5000")
    print("🔄 按 Ctrl+C 停止服务器")
    print("-" * 50)
    
    # 延迟打开浏览器
    Timer(2.0, open_browser).start()
    
    try:
        # 启动Flask应用
        from app import app, initialize_processor
        initialize_processor()
        app.run(debug=False, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n\n👋 服务器已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
