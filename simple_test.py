#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np

# 直接测试CSV数据
print("直接测试CSV数据...")

# 读取CSV
df = pd.read_csv('2025062420.010.csv', header=None)
print(f"CSV形状: {df.shape}")
print(f"数据范围: {df.values.min():.2f} - {df.values.max():.2f}")

# 检查四个角的值
print(f"左上角 [0,0]: {df.iloc[0,0]:.2f}")
print(f"右上角 [0,-1]: {df.iloc[0,-1]:.2f}")
print(f"左下角 [-1,0]: {df.iloc[-1,0]:.2f}")
print(f"右下角 [-1,-1]: {df.iloc[-1,-1]:.2f}")

# 统计非零值
non_zero_count = np.count_nonzero(df.values)
total_count = df.size
print(f"非零值: {non_zero_count}/{total_count} ({non_zero_count/total_count*100:.1f}%)")

print("测试完成!")
