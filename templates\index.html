<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>气象降雨预报可视化系统</title>
    
    <!-- Leaflet -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

    <!-- Leaflet Heat Plugin -->
    <script src="https://unpkg.com/leaflet.heat@0.2.0/dist/leaflet-heat.js"></script>
    
    <!-- 样式 -->
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background-color: #f5f5f5;
        }
        
        #map {
            position: absolute;
            top: 0;
            bottom: 0;
            width: 100%;
        }
        
        .control-panel {
            position: absolute;
            top: 10px;
            left: 10px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            max-width: 300px;
        }
        
        .control-panel h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 18px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .stat-item {
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            text-align: center;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 2px;
        }
        
        .stat-value {
            font-size: 14px;
            font-weight: bold;
            color: #333;
        }
        
        .legend {
            margin-top: 15px;
        }
        
        .legend-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }
        
        .legend-scale {
            display: flex;
            height: 20px;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 5px;
        }
        
        .legend-labels {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #666;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 2000;
        }
        
        .error {
            color: #d32f2f;
            background: #ffebee;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        
        .info-popup {
            background: white;
            padding: 10px;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .info-popup h4 {
            margin: 0 0 8px 0;
            color: #333;
        }
        
        .info-popup p {
            margin: 4px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <!-- 地图容器 -->
    <div id="map"></div>
    
    <!-- 控制面板 -->
    <div class="control-panel">
        <h3>🌧️ 降雨预报数据</h3>
        
        <!-- 统计信息 -->
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-label">最小值</div>
                <div class="stat-value" id="min-rainfall">-</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">最大值</div>
                <div class="stat-value" id="max-rainfall">-</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">平均值</div>
                <div class="stat-value" id="mean-rainfall">-</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">数据点</div>
                <div class="stat-value" id="total-points">-</div>
            </div>
        </div>
        
        <!-- 图例 -->
        <div class="legend">
            <div class="legend-title">降雨强度 (mm)</div>
            <div class="legend-scale" id="legend-scale"></div>
            <div class="legend-labels">
                <span id="legend-min">0</span>
                <span id="legend-max">1</span>
            </div>
        </div>
        
        <!-- 错误信息 -->
        <div id="error-message" class="error" style="display: none;"></div>
    </div>
    
    <!-- 加载提示 -->
    <div id="loading" class="loading">
        <div>正在加载降雨数据...</div>
    </div>

    <script>
        // 全局变量
        let map;
        let rainfallData = null;
        let stats = null;
        let heatLayer = null;

        // 初始化地图
        function initializeMap() {
            // 创建Leaflet地图
            map = L.map('map').setView([34.25, 108.75], 8);

            // 添加OpenStreetMap瓦片图层
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 18
            }).addTo(map);

            // 添加比例尺
            L.control.scale({
                metric: true,
                imperial: false
            }).addTo(map);

            // 地图加载完成后加载数据
            loadRainfallData();
        }
        
        // 加载降雨数据
        async function loadRainfallData() {
            try {
                // 获取统计信息
                const statsResponse = await fetch('/api/rainfall/stats');
                const statsData = await statsResponse.json();

                if (statsData.success) {
                    stats = statsData.stats;
                    updateStatsDisplay();
                    updateLegend();
                }

                // 获取热力图数据
                const dataResponse = await fetch('/api/rainfall/data?format=heatmap');
                const responseData = await dataResponse.json();

                if (responseData.success) {
                    rainfallData = responseData.data;
                    addRainfallLayer();
                    hideLoading();
                } else {
                    showError('加载数据失败: ' + responseData.error);
                }

            } catch (error) {
                showError('网络错误: ' + error.message);
            }
        }
        
        // 添加降雨图层
        function addRainfallLayer() {
            if (!rainfallData || !stats) return;

            // 创建热力图图层
            heatLayer = L.heatLayer(rainfallData, {
                radius: 25,
                blur: 15,
                maxZoom: 17,
                max: stats.max_rainfall,
                gradient: {
                    0.0: '#313695',
                    0.2: '#4575b4',
                    0.4: '#74add1',
                    0.6: '#abd9e9',
                    0.8: '#fee090',
                    1.0: '#d73027'
                }
            }).addTo(map);

            // 添加点击事件处理
            map.on('click', function(e) {
                const lat = e.latlng.lat;
                const lng = e.latlng.lng;

                // 查找最近的数据点
                let closestPoint = null;
                let minDistance = Infinity;

                rainfallData.forEach(point => {
                    const distance = Math.sqrt(
                        Math.pow(point[0] - lat, 2) +
                        Math.pow(point[1] - lng, 2)
                    );
                    if (distance < minDistance) {
                        minDistance = distance;
                        closestPoint = point;
                    }
                });

                if (closestPoint && minDistance < 0.05) { // 在合理范围内
                    const popup = L.popup()
                        .setLatLng([closestPoint[0], closestPoint[1]])
                        .setContent(`
                            <div class="info-popup">
                                <h4>降雨信息</h4>
                                <p><strong>降雨量:</strong> ${closestPoint[2].toFixed(2)} mm</p>
                                <p><strong>纬度:</strong> ${closestPoint[0].toFixed(4)}°</p>
                                <p><strong>经度:</strong> ${closestPoint[1].toFixed(4)}°</p>
                            </div>
                        `)
                        .openOn(map);
                }
            });
        }
        
        // 更新统计信息显示
        function updateStatsDisplay() {
            if (!stats) return;
            
            document.getElementById('min-rainfall').textContent = stats.min_rainfall.toFixed(2);
            document.getElementById('max-rainfall').textContent = stats.max_rainfall.toFixed(2);
            document.getElementById('mean-rainfall').textContent = stats.mean_rainfall.toFixed(2);
            document.getElementById('total-points').textContent = stats.total_points.toLocaleString();
        }
        
        // 更新图例
        function updateLegend() {
            if (!stats) return;
            
            const legendScale = document.getElementById('legend-scale');
            const legendMin = document.getElementById('legend-min');
            const legendMax = document.getElementById('legend-max');
            
            // 创建渐变色条
            const gradient = 'linear-gradient(to right, #313695, #4575b4, #74add1, #abd9e9, #fee090, #d73027)';
            legendScale.style.background = gradient;
            
            legendMin.textContent = stats.min_rainfall.toFixed(1);
            legendMax.textContent = stats.max_rainfall.toFixed(1);
        }
        
        // 显示错误信息
        function showError(message) {
            const errorElement = document.getElementById('error-message');
            errorElement.textContent = message;
            errorElement.style.display = 'block';
            hideLoading();
        }
        
        // 隐藏加载提示
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeMap();
        });
    </script>
</body>
</html>
