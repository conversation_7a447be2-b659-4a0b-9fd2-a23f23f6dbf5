<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>气象降雨预报可视化系统</title>
    
    <!-- Leaflet -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

    <!-- Leaflet Heat Plugin -->
    <script src="https://unpkg.com/leaflet.heat@0.2.0/dist/leaflet-heat.js"></script>
    
    <!-- 样式 -->
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background-color: #f5f5f5;
        }
        
        #map {
            position: absolute;
            top: 0;
            bottom: 0;
            width: 100%;
        }
        
        .control-panel {
            position: absolute;
            top: 10px;
            left: 10px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            max-width: 300px;
        }
        
        .control-panel h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 18px;
        }

        .control-buttons {
            margin-bottom: 15px;
        }

        .control-buttons button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 12px;
            margin: 2px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .control-buttons button:hover {
            background: #0056b3;
        }

        .control-buttons button.active {
            background: #28a745;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .stat-item {
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            text-align: center;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 2px;
        }
        
        .stat-value {
            font-size: 14px;
            font-weight: bold;
            color: #333;
        }
        
        .legend {
            margin-top: 15px;
        }
        
        .legend-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }
        
        .legend-scale {
            display: flex;
            height: 20px;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 5px;
        }
        
        .legend-labels {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #666;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 2000;
        }
        
        .error {
            color: #d32f2f;
            background: #ffebee;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        
        .info-popup {
            background: white;
            padding: 10px;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .info-popup h4 {
            margin: 0 0 8px 0;
            color: #333;
        }
        
        .info-popup p {
            margin: 4px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <!-- 地图容器 -->
    <div id="map"></div>
    
    <!-- 控制面板 -->
    <div class="control-panel">
        <h3>🌧️ 降雨预报数据</h3>

        <!-- 控制按钮 -->
        <div class="control-buttons">
            <button id="toggle-heatmap" class="active">热力图</button>
            <button id="toggle-grid">网格</button>
            <button id="toggle-labels">数值</button>
        </div>

        <!-- 统计信息 -->
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-label">最小值</div>
                <div class="stat-value" id="min-rainfall">-</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">最大值</div>
                <div class="stat-value" id="max-rainfall">-</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">平均值</div>
                <div class="stat-value" id="mean-rainfall">-</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">数据点</div>
                <div class="stat-value" id="total-points">-</div>
            </div>
        </div>
        
        <!-- 图例 -->
        <div class="legend">
            <div class="legend-title">降雨强度 (mm)</div>
            <div class="legend-scale" id="legend-scale"></div>
            <div class="legend-labels">
                <span id="legend-min">0</span>
                <span id="legend-max">1</span>
            </div>
        </div>
        
        <!-- 错误信息 -->
        <div id="error-message" class="error" style="display: none;"></div>
    </div>
    
    <!-- 加载提示 -->
    <div id="loading" class="loading">
        <div>正在加载降雨数据...</div>
    </div>

    <script>
        // 全局变量
        let map;
        let rainfallData = null;
        let stats = null;
        let heatLayer = null;
        let gridLayer = null;
        let labelsLayer = null;
        let gridData = null;
        let labelsData = null;

        // 图层显示状态
        let showHeatmap = true;
        let showGrid = false;
        let showLabels = false;

        // 初始化地图
        function initializeMap() {
            // 创建Leaflet地图
            map = L.map('map').setView([34.25, 108.75], 8);

            // 添加OpenStreetMap瓦片图层
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 18
            }).addTo(map);

            // 添加比例尺
            L.control.scale({
                metric: true,
                imperial: false
            }).addTo(map);

            // 地图加载完成后加载数据
            loadRainfallData();
        }
        
        // 加载降雨数据
        async function loadRainfallData() {
            try {
                // 获取统计信息
                const statsResponse = await fetch('/api/rainfall/stats');
                const statsData = await statsResponse.json();

                if (statsData.success) {
                    stats = statsData.stats;
                    updateStatsDisplay();
                    updateLegend();
                }

                // 获取热力图数据
                const heatmapResponse = await fetch('/api/rainfall/data?format=heatmap');
                const heatmapData = await heatmapResponse.json();

                if (heatmapData.success) {
                    rainfallData = heatmapData.data;
                }

                // 获取网格数据
                const gridResponse = await fetch('/api/rainfall/grid');
                const gridResponseData = await gridResponse.json();

                if (gridResponseData.success) {
                    gridData = gridResponseData.grid;
                }

                // 获取标签数据
                const labelsResponse = await fetch('/api/rainfall/labels');
                const labelsResponseData = await labelsResponse.json();

                if (labelsResponseData.success) {
                    labelsData = labelsResponseData.labels;
                }

                // 添加所有图层
                addAllLayers();
                setupControls();
                hideLoading();

            } catch (error) {
                showError('网络错误: ' + error.message);
            }
        }
        
        // 添加所有图层
        function addAllLayers() {
            // 添加热力图图层
            if (rainfallData && stats) {
                heatLayer = L.heatLayer(rainfallData, {
                    radius: 25,
                    blur: 15,
                    maxZoom: 17,
                    max: stats.max_rainfall,
                    gradient: {
                        0.0: '#FFFFFF',    // 白色 - 无降雨
                        0.1: '#9FEF8C',    // 浅绿 - 小雨 (159, 239, 140)
                        0.2: '#3BBA3B',    // 深绿 - 中雨 (59, 186, 59)
                        0.4: '#5FBCFD',    // 浅蓝 - 大雨 (95, 188, 253)
                        0.6: '#0100FB',    // 深蓝 - 暴雨 (1, 0, 251)
                        0.8: '#F801F5',    // 紫红 - 大暴雨 (248, 1, 245)
                        1.0: '#7B043E'     // 深红 - 特大暴雨 (123, 4, 62)
                    }
                });

                if (showHeatmap) {
                    heatLayer.addTo(map);
                }
            }

            // 添加网格图层
            if (gridData) {
                gridLayer = L.geoJSON(gridData, {
                    style: {
                        color: '#333333',        // 深灰色边界
                        weight: 1,               // 线宽
                        opacity: 0.8,            // 线条透明度
                        fillOpacity: 0           // 完全透明填充
                    },
                    onEachFeature: function(feature, layer) {
                        // 添加点击事件
                        layer.on('click', function(e) {
                            const props = feature.properties;
                            const popup = L.popup()
                                .setLatLng(e.latlng)
                                .setContent(`
                                    <div class="info-popup">
                                        <h4>网格信息</h4>
                                        <p><strong>降雨量:</strong> ${props.rainfall.toFixed(2)} mm</p>
                                        <p><strong>网格位置:</strong> 行${props.row}, 列${props.col}</p>
                                        <p><strong>中心坐标:</strong> ${props.lat_center.toFixed(4)}°N, ${props.lon_center.toFixed(4)}°E</p>
                                    </div>
                                `)
                                .openOn(map);
                        });

                        // 添加悬停效果
                        layer.on('mouseover', function(e) {
                            layer.setStyle({
                                weight: 2,
                                color: '#000000'
                            });
                        });

                        layer.on('mouseout', function(e) {
                            layer.setStyle({
                                weight: 1,
                                color: '#333333'
                            });
                        });
                    }
                });

                if (showGrid) {
                    gridLayer.addTo(map);
                }
            }

            // 添加标签图层
            if (labelsData) {
                labelsLayer = L.layerGroup();

                labelsData.forEach(label => {
                    const marker = L.marker([label.lat, label.lon], {
                        icon: L.divIcon({
                            className: 'rainfall-label',
                            html: `<div style="
                                background: rgba(255,255,255,0.8);
                                border: 1px solid #333;
                                border-radius: 3px;
                                padding: 2px 4px;
                                font-size: 10px;
                                font-weight: bold;
                                text-align: center;
                                color: #333;
                                white-space: nowrap;
                            ">${label.text}</div>`,
                            iconSize: [30, 15],
                            iconAnchor: [15, 7]
                        })
                    });

                    labelsLayer.addLayer(marker);
                });

                if (showLabels) {
                    labelsLayer.addTo(map);
                }
            }
        }
        
        // 设置控制按钮
        function setupControls() {
            // 热力图控制
            document.getElementById('toggle-heatmap').addEventListener('click', function() {
                showHeatmap = !showHeatmap;
                this.classList.toggle('active', showHeatmap);

                if (showHeatmap && heatLayer) {
                    heatLayer.addTo(map);
                } else if (heatLayer) {
                    map.removeLayer(heatLayer);
                }
            });

            // 网格控制
            document.getElementById('toggle-grid').addEventListener('click', function() {
                showGrid = !showGrid;
                this.classList.toggle('active', showGrid);

                if (showGrid && gridLayer) {
                    gridLayer.addTo(map);
                } else if (gridLayer) {
                    map.removeLayer(gridLayer);
                }
            });

            // 标签控制
            document.getElementById('toggle-labels').addEventListener('click', function() {
                showLabels = !showLabels;
                this.classList.toggle('active', showLabels);

                if (showLabels && labelsLayer) {
                    labelsLayer.addTo(map);
                } else if (labelsLayer) {
                    map.removeLayer(labelsLayer);
                }
            });
        }

        // 更新统计信息显示
        function updateStatsDisplay() {
            if (!stats) return;

            document.getElementById('min-rainfall').textContent = stats.min_rainfall.toFixed(2);
            document.getElementById('max-rainfall').textContent = stats.max_rainfall.toFixed(2);
            document.getElementById('mean-rainfall').textContent = stats.mean_rainfall.toFixed(2);
            document.getElementById('total-points').textContent = stats.total_points.toLocaleString();
        }
        
        // 更新图例
        function updateLegend() {
            if (!stats) return;

            const legendScale = document.getElementById('legend-scale');
            const legendMin = document.getElementById('legend-min');
            const legendMax = document.getElementById('legend-max');

            // 创建中国气象标准渐变色条
            const gradient = 'linear-gradient(to right, #FFFFFF, #9FEF8C, #3BBA3B, #5FBCFD, #0100FB, #F801F5, #7B043E)';
            legendScale.style.background = gradient;

            legendMin.textContent = stats.min_rainfall.toFixed(1);
            legendMax.textContent = stats.max_rainfall.toFixed(1);
        }
        
        // 显示错误信息
        function showError(message) {
            const errorElement = document.getElementById('error-message');
            errorElement.textContent = message;
            errorElement.style.display = 'block';
            hideLoading();
        }
        
        // 隐藏加载提示
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeMap();
        });
    </script>
</body>
</html>
