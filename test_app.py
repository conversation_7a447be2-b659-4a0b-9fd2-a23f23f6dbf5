#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的测试应用
"""

from flask import Flask, jsonify, render_template_string
from flask_cors import CORS
import json
import os
from data_processor import RainfallDataProcessor

app = Flask(__name__)
CORS(app)

# 简化的HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>气象降雨预报可视化系统</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://unpkg.com/leaflet.heat@0.2.0/dist/leaflet-heat.js"></script>
    <style>
        body { margin: 0; padding: 0; font-family: Arial, sans-serif; }
        #map { height: 100vh; width: 100%; }
        .info-panel {
            position: absolute; top: 10px; left: 10px; background: white;
            padding: 15px; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000; max-width: 250px;
        }
        .loading { text-align: center; padding: 20px; }
    </style>
</head>
<body>
    <div id="map"></div>
    <div class="info-panel">
        <h3>🌧️ 降雨预报数据</h3>
        <div id="stats">加载中...</div>
    </div>

    <script>
        let map = L.map('map').setView([34.25, 108.75], 8);
        
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);
        
        // 加载数据
        fetch('/api/test')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('stats').innerHTML = `
                        <p>数据点: ${data.stats.total_points}</p>
                        <p>降雨范围: ${data.stats.min_rainfall.toFixed(2)} - ${data.stats.max_rainfall.toFixed(2)} mm</p>
                        <p>平均降雨: ${data.stats.mean_rainfall.toFixed(2)} mm</p>
                    `;
                    
                    // 添加热力图
                    L.heatLayer(data.heatmap_data, {
                        radius: 20,
                        blur: 15,
                        maxZoom: 17,
                        max: data.stats.max_rainfall,
                        gradient: {
                            0.0: '#313695',
                            0.2: '#4575b4', 
                            0.4: '#74add1',
                            0.6: '#abd9e9',
                            0.8: '#fee090',
                            1.0: '#d73027'
                        }
                    }).addTo(map);
                }
            })
            .catch(error => {
                document.getElementById('stats').innerHTML = '<p style="color: red;">加载失败: ' + error.message + '</p>';
            });
    </script>
</body>
</html>
"""

# 全局数据处理器
processor = None

def initialize_processor():
    global processor
    csv_file = '2025062420.010.csv'
    if os.path.exists(csv_file):
        processor = RainfallDataProcessor(csv_file)
        processor.load_csv_data()
        print("数据处理器初始化成功")
        return True
    else:
        print(f"错误: 找不到CSV文件 {csv_file}")
        return False

@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/test')
def test_api():
    try:
        if processor is None:
            return jsonify({'success': False, 'error': '数据处理器未初始化'})
        
        stats = processor.get_statistics()
        heatmap_data = processor.get_heatmap_data()
        
        return jsonify({
            'success': True,
            'stats': stats,
            'heatmap_data': heatmap_data[:1000]  # 限制数据量以提高性能
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    print("启动测试服务器...")
    if initialize_processor():
        print("访问 http://localhost:5000 查看地图")
        app.run(debug=True, host='0.0.0.0', port=5000)
    else:
        print("初始化失败")
