#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的测试应用
"""

from flask import Flask, jsonify, render_template_string
from flask_cors import CORS
import json
import os
from data_processor import RainfallDataProcessor

app = Flask(__name__)
CORS(app)

# 简化的HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>气象降雨预报可视化系统</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://unpkg.com/leaflet.heat@0.2.0/dist/leaflet-heat.js"></script>
    <style>
        body { margin: 0; padding: 0; font-family: Arial, sans-serif; }
        #map { height: 100vh; width: 100%; }
        .info-panel {
            position: absolute; top: 10px; left: 10px; background: white;
            padding: 15px; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000; max-width: 280px;
        }
        .loading { text-align: center; padding: 20px; }
        .control-buttons { margin-bottom: 15px; }
        .control-buttons button {
            background: #007bff; color: white; border: none; padding: 6px 10px;
            margin: 2px; border-radius: 3px; cursor: pointer; font-size: 11px;
        }
        .control-buttons button:hover { background: #0056b3; }
        .control-buttons button.active { background: #28a745; }
    </style>
</head>
<body>
    <div id="map"></div>
    <div class="info-panel">
        <h3>🌧️ 降雨预报数据</h3>
        <div class="control-buttons">
            <button id="toggle-heatmap" class="active">热力图</button>
            <button id="toggle-grid">网格</button>
            <button id="toggle-labels">数值</button>
        </div>
        <div id="stats">加载中...</div>
    </div>

    <script>
        let map = L.map('map').setView([34.25, 108.75], 8);
        let heatLayer = null;
        let gridLayer = null;
        let labelsLayer = null;
        let showHeatmap = true;
        let showGrid = false;
        let showLabels = false;

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);

        // 加载数据
        Promise.all([
            fetch('/api/test').then(r => r.json()),
            fetch('/api/rainfall/grid').then(r => r.json()),
            fetch('/api/rainfall/labels').then(r => r.json())
        ]).then(([testData, gridData, labelsData]) => {
            if (testData.success) {
                document.getElementById('stats').innerHTML = `
                    <p>网格: ${testData.stats.grid_dimensions[0]} × ${testData.stats.grid_dimensions[1]}</p>
                    <p>数据点: ${testData.stats.total_points}</p>
                    <p>降雨范围: ${testData.stats.min_rainfall.toFixed(2)} - ${testData.stats.max_rainfall.toFixed(2)} mm</p>
                    <p>平均降雨: ${testData.stats.mean_rainfall.toFixed(2)} mm</p>
                `;

                // 创建热力图
                heatLayer = L.heatLayer(testData.heatmap_data, {
                    radius: 20,
                    blur: 15,
                    maxZoom: 17,
                    max: testData.stats.max_rainfall,
                    gradient: {
                        0.0: '#313695',
                        0.2: '#4575b4',
                        0.4: '#74add1',
                        0.6: '#abd9e9',
                        0.8: '#fee090',
                        1.0: '#d73027'
                    }
                }).addTo(map);

                // 创建网格图层
                if (gridData.success) {
                    gridLayer = L.geoJSON(gridData.grid, {
                        style: {
                            color: '#333333',
                            weight: 1,
                            opacity: 0.8,
                            fillOpacity: 0
                        },
                        onEachFeature: function(feature, layer) {
                            layer.on('click', function(e) {
                                const props = feature.properties;
                                L.popup()
                                    .setLatLng(e.latlng)
                                    .setContent(`
                                        <div><h4>网格信息</h4>
                                        <p><strong>降雨量:</strong> ${props.rainfall.toFixed(2)} mm</p>
                                        <p><strong>位置:</strong> 行${props.row}, 列${props.col}</p>
                                        <p><strong>坐标:</strong> ${props.lat_center.toFixed(4)}°N, ${props.lon_center.toFixed(4)}°E</p></div>
                                    `)
                                    .openOn(map);
                            });
                        }
                    });
                }

                // 创建标签图层
                if (labelsData.success) {
                    labelsLayer = L.layerGroup();
                    labelsData.labels.forEach(label => {
                        const marker = L.marker([label.lat, label.lon], {
                            icon: L.divIcon({
                                className: 'rainfall-label',
                                html: `<div style="background: rgba(255,255,255,0.9); border: 1px solid #333; border-radius: 3px; padding: 1px 3px; font-size: 9px; font-weight: bold; color: #333;">${label.text}</div>`,
                                iconSize: [25, 12],
                                iconAnchor: [12, 6]
                            })
                        });
                        labelsLayer.addLayer(marker);
                    });
                }

                // 设置控制按钮
                setupControls();
            }
        }).catch(error => {
            document.getElementById('stats').innerHTML = '<p style="color: red;">加载失败: ' + error.message + '</p>';
        });

        function setupControls() {
            document.getElementById('toggle-heatmap').addEventListener('click', function() {
                showHeatmap = !showHeatmap;
                this.classList.toggle('active', showHeatmap);
                if (showHeatmap && heatLayer) heatLayer.addTo(map);
                else if (heatLayer) map.removeLayer(heatLayer);
            });

            document.getElementById('toggle-grid').addEventListener('click', function() {
                showGrid = !showGrid;
                this.classList.toggle('active', showGrid);
                if (showGrid && gridLayer) gridLayer.addTo(map);
                else if (gridLayer) map.removeLayer(gridLayer);
            });

            document.getElementById('toggle-labels').addEventListener('click', function() {
                showLabels = !showLabels;
                this.classList.toggle('active', showLabels);
                if (showLabels && labelsLayer) labelsLayer.addTo(map);
                else if (labelsLayer) map.removeLayer(labelsLayer);
            });
        }
    </script>
</body>
</html>
"""

# 全局数据处理器
processor = None

def initialize_processor():
    global processor
    csv_file = '2025062420.010.csv'
    if os.path.exists(csv_file):
        processor = RainfallDataProcessor(csv_file)
        processor.load_csv_data()
        print("数据处理器初始化成功")
        return True
    else:
        print(f"错误: 找不到CSV文件 {csv_file}")
        return False

@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/test')
def test_api():
    try:
        if processor is None:
            return jsonify({'success': False, 'error': '数据处理器未初始化'})

        stats = processor.get_statistics()
        # 使用更低的阈值以显示更多数据点
        heatmap_data = processor.get_heatmap_data(min_threshold=0.01)

        return jsonify({
            'success': True,
            'stats': stats,
            'heatmap_data': heatmap_data[:2000]  # 增加数据量限制
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/rainfall/grid')
def get_grid_data():
    try:
        if processor is None:
            return jsonify({'success': False, 'error': '数据处理器未初始化'})

        grid_geojson = processor.get_grid_geojson()

        return jsonify({
            'success': True,
            'grid': grid_geojson
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/rainfall/labels')
def get_grid_labels():
    try:
        if processor is None:
            return jsonify({'success': False, 'error': '数据处理器未初始化'})

        labels = processor.get_grid_labels_data()

        return jsonify({
            'success': True,
            'labels': labels
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    print("启动测试服务器...")
    if initialize_processor():
        print("访问 http://localhost:5000 查看地图")
        app.run(debug=True, host='0.0.0.0', port=5000)
    else:
        print("初始化失败")
