#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试中国气象标准颜色方案
"""

from data_processor import RainfallDataProcessor
import numpy as np

def test_color_scheme():
    """测试颜色方案功能"""
    print("测试中国气象标准颜色方案...")
    
    processor = RainfallDataProcessor('2025062420.010.csv')
    processor.load_csv_data()
    
    # 测试颜色方案获取
    gradient = processor.get_china_meteorological_gradient()
    print("✅ 中国气象标准渐变配置:")
    for position, color in gradient.items():
        print(f"  {position}: {color}")
    
    # 测试不同降雨量的颜色映射
    test_rainfalls = [0.0, 0.5, 1.5, 5.0, 10.0, 20.0, 50.0, 80.0]
    
    print("\n✅ 降雨量颜色映射测试:")
    for rainfall in test_rainfalls:
        level = processor.get_rainfall_intensity_level(rainfall)
        color = processor.get_rainfall_color(rainfall)
        print(f"  {rainfall:4.1f}mm/h -> {level:8s} -> {color}")
    
    # 分析实际数据的颜色分布
    print("\n✅ 实际数据颜色分布分析:")
    
    # 统计各强度等级的数据点数量
    level_counts = {}
    color_counts = {}
    
    for i in range(processor.lat_points):
        for j in range(processor.lon_points):
            rainfall = processor.rainfall_data[i, j]
            level = processor.get_rainfall_intensity_level(rainfall)
            color = processor.get_rainfall_color(rainfall)
            
            level_counts[level] = level_counts.get(level, 0) + 1
            color_counts[color] = color_counts.get(color, 0) + 1
    
    print("降雨强度等级分布:")
    for level, count in level_counts.items():
        percentage = count / processor.rainfall_data.size * 100
        print(f"  {level:8s}: {count:5d} 点 ({percentage:5.1f}%)")
    
    print("\n颜色使用分布:")
    for color, count in color_counts.items():
        percentage = count / processor.rainfall_data.size * 100
        print(f"  {color}: {count:5d} 点 ({percentage:5.1f}%)")
    
    # 测试标签数据的颜色信息
    labels = processor.get_grid_labels_data()
    print(f"\n✅ 标签数据测试: {len(labels)} 个标签")
    
    if labels:
        print("前5个标签的颜色信息:")
        for i, label in enumerate(labels[:5]):
            print(f"  标签{i+1}: {label['text']}mm -> {label['intensity_level']} -> {label['color']}")
    
    # 验证颜色代码的正确性
    print("\n✅ 颜色代码验证:")
    expected_colors = {
        '#FFFFFF': (255, 255, 255),
        '#9FEF8C': (159, 239, 140),
        '#3BBA3B': (59, 186, 59),
        '#5FBCFD': (95, 188, 253),
        '#0100FB': (1, 0, 251),
        '#F801F5': (248, 1, 245),
        '#7B043E': (123, 4, 62)
    }
    
    for hex_color, expected_rgb in expected_colors.items():
        # 将十六进制转换为RGB验证
        hex_clean = hex_color.lstrip('#')
        actual_rgb = tuple(int(hex_clean[i:i+2], 16) for i in (0, 2, 4))
        
        if actual_rgb == expected_rgb:
            print(f"  {hex_color} -> RGB{actual_rgb} ✅")
        else:
            print(f"  {hex_color} -> RGB{actual_rgb} ❌ (期望: {expected_rgb})")
    
    print("\n🎉 颜色方案测试完成!")

def generate_color_test_html():
    """生成颜色测试HTML文件"""
    processor = RainfallDataProcessor('2025062420.010.csv')
    processor.load_csv_data()
    
    # 获取一些样本数据点
    sample_points = []
    for i in range(0, processor.lat_points, 10):
        for j in range(0, processor.lon_points, 20):
            rainfall = processor.rainfall_data[i, j]
            if rainfall > 0.01:  # 只取有降雨的点
                lat = processor.lat_max - i * processor.grid_spacing
                lon = processor.lon_min + j * processor.grid_spacing
                level = processor.get_rainfall_intensity_level(rainfall)
                color = processor.get_rainfall_color(rainfall)
                
                sample_points.append({
                    'lat': lat,
                    'lon': lon,
                    'rainfall': rainfall,
                    'level': level,
                    'color': color
                })
    
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>颜色方案测试</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .sample-point {{ margin: 5px 0; padding: 5px; border: 1px solid #ddd; }}
            .color-box {{ display: inline-block; width: 30px; height: 20px; margin-right: 10px; border: 1px solid #000; }}
        </style>
    </head>
    <body>
        <h1>降雨数据颜色方案测试</h1>
        <p>样本数据点: {len(sample_points)} 个</p>
        
        <h2>颜色映射示例</h2>
    """
    
    for i, point in enumerate(sample_points[:20]):  # 只显示前20个点
        html_content += f"""
        <div class="sample-point">
            <span class="color-box" style="background-color: {point['color']};"></span>
            点{i+1}: ({point['lat']:.3f}°N, {point['lon']:.3f}°E) 
            降雨量: {point['rainfall']:.2f}mm/h 
            等级: {point['level']} 
            颜色: {point['color']}
        </div>
        """
    
    html_content += """
    </body>
    </html>
    """
    
    with open('color_test_samples.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("✅ 生成颜色测试HTML文件: color_test_samples.html")

if __name__ == "__main__":
    test_color_scheme()
    generate_color_test_html()
