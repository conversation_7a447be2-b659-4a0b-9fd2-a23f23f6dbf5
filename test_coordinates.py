#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试坐标映射的正确性
"""

from data_processor import RainfallDataProcessor
import numpy as np

def test_coordinate_mapping():
    """测试坐标映射"""
    print("测试坐标映射...")
    
    processor = RainfallDataProcessor('2025062420.010.csv')
    processor.load_csv_data()
    
    # 检查数据维度
    print(f"CSV数据维度: {processor.rainfall_data.shape}")
    print(f"预期维度: {processor.lat_points} × {processor.lon_points}")
    
    # 生成坐标网格
    lon_grid, lat_grid = processor.generate_coordinates()
    print(f"坐标网格维度: {lat_grid.shape}")
    
    # 检查坐标范围
    print(f"纬度范围: {lat_grid.min():.4f} - {lat_grid.max():.4f} (预期: {processor.lat_min} - {processor.lat_max})")
    print(f"经度范围: {lon_grid.min():.4f} - {lon_grid.max():.4f} (预期: {processor.lon_min} - {processor.lon_max})")
    
    # 检查四个角点的坐标和数值
    print("\n四个角点检查:")
    corners = [
        (0, 0, "左上角(北西)"),
        (0, -1, "右上角(北东)"),
        (-1, 0, "左下角(南西)"),
        (-1, -1, "右下角(南东)")
    ]
    
    for i, j, desc in corners:
        lat = lat_grid[i, j]
        lon = lon_grid[i, j]
        rainfall = processor.rainfall_data[i, j]
        print(f"  {desc}: ({lat:.4f}°N, {lon:.4f}°E) = {rainfall:.2f}mm")
    
    # 检查中心点
    center_i = processor.lat_points // 2
    center_j = processor.lon_points // 2
    center_lat = lat_grid[center_i, center_j]
    center_lon = lon_grid[center_i, center_j]
    center_rainfall = processor.rainfall_data[center_i, center_j]
    print(f"  中心点: ({center_lat:.4f}°N, {center_lon:.4f}°E) = {center_rainfall:.2f}mm")
    
    # 获取热力图数据并检查前几个点
    heatmap_data = processor.get_heatmap_data()
    print(f"\n热力图数据点数: {len(heatmap_data)}")
    print("前5个热力图数据点:")
    for i, point in enumerate(heatmap_data[:5]):
        print(f"  点{i+1}: ({point[0]:.4f}°N, {point[1]:.4f}°E) = {point[2]:.2f}mm")
    
    # 检查网格数据的一致性
    grid_geojson = processor.get_grid_geojson()
    print(f"\n网格GeoJSON特征数: {len(grid_geojson['features'])}")
    
    # 检查第一个网格单元
    first_feature = grid_geojson['features'][0]
    props = first_feature['properties']
    coords = first_feature['geometry']['coordinates'][0]
    print(f"第一个网格单元:")
    print(f"  属性: 行{props['row']}, 列{props['col']}, 降雨量{props['rainfall']:.2f}mm")
    print(f"  中心: ({props['lat_center']:.4f}°N, {props['lon_center']:.4f}°E)")
    print(f"  边界: {coords[0]} -> {coords[2]}")
    
    # 验证CSV第一个值与网格第一个值是否一致
    csv_first_value = processor.rainfall_data[0, 0]
    grid_first_value = props['rainfall']
    print(f"\n一致性检查:")
    print(f"  CSV[0,0]: {csv_first_value:.2f}mm")
    print(f"  网格[0,0]: {grid_first_value:.2f}mm")
    print(f"  一致: {'✅' if abs(csv_first_value - grid_first_value) < 0.001 else '❌'}")

if __name__ == "__main__":
    test_coordinate_mapping()
