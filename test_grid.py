#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试网格功能
"""

from data_processor import RainfallDataProcessor
import json

def test_grid_functionality():
    """测试网格功能"""
    print("测试网格功能...")
    
    # 创建数据处理器
    processor = RainfallDataProcessor('2025062420.010.csv')
    
    # 加载数据
    data = processor.load_csv_data()
    if data is None:
        print("❌ 数据加载失败")
        return
    
    print("✅ 数据加载成功")
    
    # 测试统计信息
    stats = processor.get_statistics()
    print(f"✅ 统计信息: {len(stats)} 项")
    print(f"   网格尺寸: {stats['grid_dimensions']}")
    print(f"   坐标范围: 纬度 {stats['lat_range']}, 经度 {stats['lon_range']}")
    
    # 测试网格GeoJSON
    grid_geojson = processor.get_grid_geojson()
    print(f"✅ 网格GeoJSON: {len(grid_geojson['features'])} 个网格单元")
    
    # 显示前几个网格单元信息
    print("前3个网格单元:")
    for i, feature in enumerate(grid_geojson['features'][:3]):
        props = feature['properties']
        coords = feature['geometry']['coordinates'][0]
        print(f"  网格{i+1}: 行{props['row']}, 列{props['col']}, 降雨量{props['rainfall']:.2f}mm")
        print(f"    坐标: {coords[0]} -> {coords[2]}")
    
    # 测试标签数据
    labels = processor.get_grid_labels_data()
    print(f"✅ 标签数据: {len(labels)} 个标签")
    
    # 显示前几个标签
    print("前5个标签:")
    for i, label in enumerate(labels[:5]):
        print(f"  标签{i+1}: ({label['lat']:.4f}, {label['lon']:.4f}) = {label['text']}mm")
    
    # 测试热力图数据
    heatmap_data = processor.get_heatmap_data()
    print(f"✅ 热力图数据: {len(heatmap_data)} 个数据点")
    
    print("\n🎉 所有测试通过!")

if __name__ == "__main__":
    test_grid_functionality()
