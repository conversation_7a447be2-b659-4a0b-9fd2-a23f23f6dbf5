#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证热力图数据的正确性
"""

from data_processor import RainfallDataProcessor
import numpy as np

def verify_heatmap_data():
    """验证热力图数据"""
    print("验证热力图数据...")
    
    processor = RainfallDataProcessor('2025062420.010.csv')
    processor.load_csv_data()
    
    # 获取不同阈值的热力图数据
    thresholds = [0.0, 0.01, 0.05, 0.1]
    
    for threshold in thresholds:
        heatmap_data = processor.get_heatmap_data(min_threshold=threshold)
        print(f"阈值 {threshold}: {len(heatmap_data)} 个数据点")
    
    # 使用最低阈值获取最多数据
    heatmap_data = processor.get_heatmap_data(min_threshold=0.0)
    print(f"\n完整热力图数据: {len(heatmap_data)} 个点")
    
    # 检查数据范围
    if heatmap_data:
        lats = [point[0] for point in heatmap_data]
        lons = [point[1] for point in heatmap_data]
        rainfalls = [point[2] for point in heatmap_data]
        
        print(f"纬度范围: {min(lats):.4f} - {max(lats):.4f}")
        print(f"经度范围: {min(lons):.4f} - {max(lons):.4f}")
        print(f"降雨范围: {min(rainfalls):.4f} - {max(rainfalls):.4f}")
    
    # 验证特定位置的数据
    print("\n验证特定位置:")
    
    # 检查CSV数据的几个已知位置
    test_positions = [
        (0, 0),    # 左上角
        (0, 50),   # 上边中间
        (0, 100),  # 右上角
        (30, 50),  # 中心
        (60, 0),   # 左下角
        (60, 100)  # 右下角
    ]
    
    for row, col in test_positions:
        if row < processor.rainfall_data.shape[0] and col < processor.rainfall_data.shape[1]:
            csv_value = processor.rainfall_data[row, col]
            
            # 计算对应的地理坐标
            lat = processor.lat_max - row * processor.grid_spacing
            lon = processor.lon_min + col * processor.grid_spacing
            
            # 在热力图数据中查找对应点
            found_point = None
            for point in heatmap_data:
                if abs(point[0] - lat) < 0.001 and abs(point[1] - lon) < 0.001:
                    found_point = point
                    break
            
            if found_point:
                print(f"  位置[{row},{col}]: CSV={csv_value:.2f}, 热力图={found_point[2]:.2f}, 坐标=({lat:.4f}, {lon:.4f}) ✅")
            else:
                print(f"  位置[{row},{col}]: CSV={csv_value:.2f}, 热力图=未找到, 坐标=({lat:.4f}, {lon:.4f}) ❌")
    
    # 统计不同降雨强度的分布
    print("\n降雨强度分布:")
    ranges = [(0, 0.1), (0.1, 0.3), (0.3, 0.5), (0.5, 0.8), (0.8, 1.0), (1.0, 2.0)]
    
    for min_val, max_val in ranges:
        count = sum(1 for point in heatmap_data if min_val <= point[2] < max_val)
        print(f"  {min_val:.1f} - {max_val:.1f} mm: {count} 个点")
    
    print("\n✅ 热力图数据验证完成")

if __name__ == "__main__":
    verify_heatmap_data()
